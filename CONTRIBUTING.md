# 项目贡献与后续开发指南

本文档为 `occt-learning-repo` 项目的开发者提供指导，旨在确保新内容与现有结构、风格和质量标准保持一致。无论是添加新模块的介绍，还是修复现有问题，都请遵循本指南。

---

## 核心开发原则

为了维护本库的质量和一致性，所有贡献者都应遵循以下核心原则：

1.  **文档先行**: 在编写任何示例代码之前，请先在 `/OCC` 目录下创建或更新对应的 `.md` 文档。清晰地阐述模块的用途、核心概念和主要API。
2.  **代码为教学服务**: `/src` 中的示例代码应简单、聚焦，其唯一目的是清晰地演示文档中介绍的核心功能。避免在一个示例中混合过多的概念。
3.  **中英双语注释**: 必须为示例代码中的关键步骤提供精准的、一一对应的英文和中文两种注释。
4.  **验证所有代码**: 任何提交或修改的示例代码，都必须在项目指定的环境 (`conda activate occ`) 中成功运行，确保其无误。
5.  **遵循计划**: 新模块的开发应遵循 [进阶计划任务表 (task_schedule_advanced.md)](./task_schedule_advanced.md) 中规划的路径，以保证学习曲线的平滑和内容的系统性。

---

## 如何开始下一个任务

以开发《进阶计划》中的第一个任务 **`GeomAPI`** 为例，标准的开发流程如下：

1.  **选择任务**: 查阅 `task_schedule_advanced.md`，确定当前要开发的目标是 **任务 5.1: `GeomAPI`**。

2.  **创建目录**: 在 `/OCC/Core` 和 `/src/Core` 目录下分别创建 `GeomAPI` 子目录。
    ```bash
    mkdir OCC/Core/GeomAPI src/Core/GeomAPI
    ```

3.  **编写文档**: 在 `/OCC/Core/GeomAPI/` 下创建 `GeomAPI.md` 文件。根据任务表的要求，解释该模块的用途（如通过点集创建样条曲线/曲面），并列出 `GeomAPI_PointsToBSplineSurface` 等核心类。

4.  **编写示例代码**: 在 `/src/Core/GeomAPI/` 下创建 `example.py` 文件。编写一个简单的脚本，演示如何使用 `GeomAPI_PointsToBSplineSurface` 从一个点阵创建一个曲面。**务必添加中英双语注释**。

5.  **测试与验证**: 运行你编写的示例脚本 `python src/Core/GeomAPI/example.py`，确保它能无错误地执行并打印出预期的成功信息。

6.  **更新计划状态 (可选)**: 如果您有权限，可以在 `task_schedule_advanced.md` 中将被完成的任务状态从“待办”更新为“已完成”。

7.  **阶段汇总**: 当一个阶段（如第五阶段）的所有核心模块都完成后，在 `/examples` 目录下创建对应的汇总示例（如 `phase_5_advanced_surfaces.py`），综合运用该阶段学习的所有技能。

## 关于自动化测试的建议

为了保证项目规模扩大后的代码质量，未来的一个高优先级任务是为本项目引入 `pytest` 测试框架。可以创建一个 `/tests` 目录，为 `/src` 中的每一个示例脚本编写一个对应的测试用例，确保所有示例始终能够正确运行。这对于项目的长期健康发展至关重要。

---

感谢您为本项目付出的努力！
