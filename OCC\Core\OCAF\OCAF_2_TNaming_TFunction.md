# OCAF Part 2: `TNaming` & `TFunction` - 参数化建模

这是OCAF框架的核心所在。`TNaming` 和 `TFunction` 这两个工具包协同工作，让我们能够将抽象的数据（如长度、宽度）与具体的几何形状关联起来，并定义它们之间的更新逻辑，从而实现参数化建模。

## 核心概念

1.  **命名形状 (Named Shape - `TNaming_NamedShape`)**: 
    这是一个特殊的属性，其唯一的作用就是将一个 `TopoDS_Shape` 几何体“附加”到一个标签上。通过这种方式，几何体就成为了OCAF文档数据树的一部分，可以被其他部分引用。

2.  **函数 (Function - `TFunction_Function`)**: 
    这可以理解为一个“黑盒子”或一个“计算器”。它定义了一种依赖关系和执行逻辑：
    - **输入 (Inputs)**: 一个函数可以依赖一个或多个其他标签作为其输入。例如，一个“创建盒子”的函数，可以依赖我们之前创建的“长度”、“宽度”、“高度”这三个标签作为输入。
    - **输出 (Outputs)**: 一个函数会将其计算结果存放在一个输出标签上。例如，“创建盒子”函数的输出，就是一个包含盒子几何体的 `TNaming_NamedShape` 属性。
    - **执行逻辑 (Driver)**: 每个函数都需要关联一个“驱动器”（Driver）。这个驱动器是一个算法对象，它包含了真正的计算代码（例如，读取输入参数，调用 `BRepPrimAPI_MakeBox`，返回结果形状）。

## 参数化工作流

一个典型的参数化流程如下：

1.  **设置参数**: 在文档中创建标签，并使用 `TDataStd_Real` 等属性来存储设计参数（如长度=100）。
2.  **设置函数**: 创建一个函数，将其输入指向上一步的参数标签，输出指向一个用于存放结果几何体的标签。
3.  **关联算法**: 为该函数设置一个“驱动器”，驱动器的代码逻辑就是“如何根据输入参数生成几何体”。
4.  **执行函数**: 调用函数的 `.Execute()` 方法。驱动器会被调用，它会读取输入参数的值，执行建模算法，并将生成的 `TopoDS_Shape` 通过 `TNaming_NamedShape` 属性写回到输出标签上。
5.  **参数修改与更新**: 当用户修改了第一步中的任何一个参数值后，函数会自动感知到其输入已“过时”（dirty）。再次调用 `.Execute()` 时，它会重新执行计算，从而更新几何体，实现参数化驱动。

## 主要类

*   **`TNaming_NamedShape`**: 用于将 `TopoDS_Shape` 存放在标签上的属性。
*   **`TFunction_Function`**: OCAF中的函数属性，用于管理依赖关系和执行。
*   **`TFunction_Driver`**: 所有函数驱动器的基类。我们需要继承这个类，并实现其核心的 `.Execute()` 方法来编写自己的算法逻辑。

在接下来的示例中，我们将把第一部分创建的参数和这一部分的函数机制结合起来，创建一个真正的、虽然简单，但功能完备的参数化盒子模型。
