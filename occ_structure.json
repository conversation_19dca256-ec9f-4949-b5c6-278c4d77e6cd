{"name": "OCC", "type": "package", "classes": [], "children": [{"name": "OCC.Core", "type": "package", "classes": [], "children": [{"name": "OCC.Core.AIS", "type": "module", "classes": ["AIS_Animation", "AIS_AnimationAxisRotation", "AIS_AnimationCamera", "AIS_AnimationObject", "AIS_AnimationProgress", "AIS_AttributeFilter", "AIS_Axis", "AIS_BadEdgeFilter", "AIS_BaseAnimationObject", "AIS_C0RegularityFilter", "AIS_CameraFrustum", "AIS_Circle", "AIS_ColorScale", "AIS_ColoredDrawer", "AIS_ColoredShape", "AIS_ConnectedInteractive", "AIS_DataMapOfIOStatus", "AIS_DataMapOfShapeDrawer", "AIS_DisplayMode", "AIS_DragAction", "AIS_ExclusionFilter", "AIS_GlobalStatus", "AIS_GraphicTool", "AIS_InteractiveContext", "AIS_InteractiveObject", "AIS_KindOfInteractive", "AIS_LightSource", "AIS_LightSourceOwner", "AIS_Line", "AIS_ListIteratorOfListOfInteractive", "AIS_ListOfInteractive", "AIS_Manipulator", "AIS_ManipulatorMode", "AIS_ManipulatorOwner", "AIS_MediaPlayer", "AIS_MouseGesture", "AIS_MouseGestureMap", "AIS_MouseSelectionSchemeMap", "AIS_MultipleConnectedInteractive", "AIS_NArray1OfEntityOwner", "AIS_NListOfEntityOwner", "AIS_NavigationMode", "AIS_Plane", "AIS_PlaneTrihedron", "AIS_Point", "AIS_PointCloud", "AIS_PointCloudOwner", "AIS_RotationMode", "AIS_RubberBand", "AIS_SelectStatus", "AIS_Selection", "AIS_SelectionModesConcurrency", "AIS_SelectionScheme", "AIS_Shape", "AIS_SignatureFilter", "AIS_StatusOfDetection", "AIS_StatusOfPick", "AIS_TextLabel", "AIS_TexturedShape", "AIS_Triangulation", "AIS_Trihedron", "AIS_TrihedronOwner", "AIS_TrihedronSelectionMode", "AIS_TypeFilter", "AIS_TypeOfAttribute", "AIS_TypeOfAxis", "AIS_TypeOfIso", "AIS_TypeOfPlane", "AIS_ViewController", "AIS_ViewCube", "AIS_ViewCubeOwner", "AIS_ViewCubeSensitive", "AIS_ViewInputBuffer", "AIS_ViewInputBufferType", "AIS_ViewSelectionTool", "AIS_WalkDelta", "AIS_WalkPart", "AIS_WalkRotation", "AIS_WalkTranslation", "AIS_XRTrackedDevice", "SwigPyIterator", "_SwigNonDynamicMeta", "ais", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.APIHeaderSection", "type": "module", "classes": ["APIHeaderSection_EditHeader", "APIHeaderSection_MakeHeader", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Adaptor2d", "type": "module", "classes": ["Adaptor2d_Curve2d", "Adaptor2d_Line2d", "Adaptor2d_OffsetCurve", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Adaptor3d", "type": "module", "classes": ["Adaptor3d_Curve", "Adaptor3d_CurveOnSurface", "Adaptor3d_HSurfaceTool", "Adaptor3d_HVertex", "Adaptor3d_InterFunc", "Adaptor3d_IsoCurve", "Adaptor3d_Surface", "Adaptor3d_TopolTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Addons", "type": "module", "classes": ["_SwigNonDynamicMeta"], "children": []}, {"name": "OCC.Core.AdvApp2Var", "type": "module", "classes": ["AdvApp2Var_ApproxAFunc2Var", "AdvApp2Var_ApproxF2var", "AdvApp2Var_Context", "AdvApp2Var_Criterion", "AdvApp2Var_CriterionRepartition", "AdvApp2Var_CriterionType", "AdvApp2Var_Data", "AdvApp2Var_Framework", "AdvApp2Var_MathBase", "AdvApp2Var_Network", "AdvApp2Var_Node", "AdvApp2Var_Patch", "AdvApp2Var_SequenceOfNode", "AdvApp2Var_SequenceOfPatch", "AdvApp2Var_SequenceOfStrip", "AdvApp2Var_Strip", "AdvApp2Var_SysBase", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AdvApprox", "type": "module", "classes": ["AdvApprox_ApproxAFunction", "AdvApprox_Cutting", "AdvApprox_DichoCutting", "AdvApprox_PrefAndRec", "AdvApprox_PrefCutting", "AdvApprox_SimpleApprox", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppBlend", "type": "module", "classes": ["AppBlend_Approx", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppCont", "type": "module", "classes": ["AppCont_Function", "AppCont_LeastSquare", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppDef", "type": "module", "classes": ["AppDef_Array1OfMultiPointConstraint", "AppDef_BSpGradient_BFGSOfMyBSplGradientOfBSplineCompute", "AppDef_BSpParFunctionOfMyBSplGradientOfBSplineCompute", "AppDef_BSpParLeastSquareOfMyBSplGradientOfBSplineCompute", "AppDef_BSplineCompute", "AppDef_Compute", "AppDef_Gradient_BFGSOfMyGradientOfCompute", "AppDef_Gradient_BFGSOfMyGradientbisOfBSplineCompute", "AppDef_Gradient_BFGSOfTheGradient", "AppDef_HArray1OfMultiPointConstraint", "AppDef_LinearCriteria", "AppDef_MultiLine", "AppDef_MultiPointConstraint", "AppDef_MyBSplGradientOfBSplineCompute", "AppDef_MyGradientOfCompute", "AppDef_MyGradientbisOfBSplineCompute", "AppDef_MyLineTool", "AppDef_ParFunctionOfMyGradientOfCompute", "AppDef_ParFunctionOfMyGradientbisOfBSplineCompute", "AppDef_ParFunctionOfTheGradient", "AppDef_ParLeastSquareOfMyGradientOfCompute", "AppDef_ParLeastSquareOfMyGradientbisOfBSplineCompute", "AppDef_ParLeastSquareOfTheGradient", "AppDef_ResConstraintOfMyGradientOfCompute", "AppDef_ResConstraintOfMyGradientbisOfBSplineCompute", "AppDef_ResConstraintOfTheGradient", "AppDef_SmoothCriterion", "AppDef_TheFunction", "AppDef_TheGradient", "AppDef_TheLeastSquares", "AppDef_TheResol", "AppDef_Variational", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppParCurves", "type": "module", "classes": ["AppParCurves_Array1OfConstraintCouple", "AppParCurves_Array1OfMultiBSpCurve", "AppParCurves_Array1OfMultiCurve", "AppParCurves_Array1OfMultiPoint", "AppParCurves_Constraint", "AppParCurves_ConstraintCouple", "AppParCurves_HArray1OfConstraintCouple", "AppParCurves_HArray1OfMultiBSpCurve", "AppParCurves_HArray1OfMultiCurve", "AppParCurves_HArray1OfMultiPoint", "AppParCurves_MultiBSpCurve", "AppParCurves_MultiCurve", "AppParCurves_MultiPoint", "AppParCurves_SequenceOfMultiBSpCurve", "AppParCurves_SequenceOfMultiCurve", "SwigPyIterator", "_SwigNonDynamicMeta", "appparcurves", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppStd", "type": "module", "classes": ["AppStd_Application", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.AppStdL", "type": "module", "classes": ["AppStdL_Application", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Approx", "type": "module", "classes": ["Approx_Array1OfAdHSurface", "Approx_Array1OfGTrsf2d", "Approx_Curve2d", "Approx_Curve3d", "Approx_CurveOnSurface", "Approx_CurvilinearParameter", "Approx_CurvlinFunc", "Approx_FitAndDivide", "Approx_FitAndDivide2d", "Approx_HArray1OfAdHSurface", "Approx_HArray1OfGTrsf2d", "Approx_MCurvesToBSpCurve", "Approx_ParametrizationType", "Approx_SameParameter", "Approx_SequenceOfHArray1OfReal", "Approx_Status", "Approx_SweepApproximation", "Approx_SweepFunction", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ApproxInt", "type": "module", "classes": ["ApproxInt_KnotTools", "ApproxInt_SvSurfaces", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Aspect", "type": "module", "classes": ["Aspect_Background", "Aspect_ColorSpace", "Aspect_DisplayConnection", "Aspect_Eye", "Aspect_FillMethod", "Aspect_GenId", "Aspect_GradientBackground", "Aspect_GradientFillMethod", "Aspect_GraphicsLibrary", "Aspect_Grid", "Aspect_GridDrawMode", "Aspect_GridType", "Aspect_HatchStyle", "Aspect_InteriorStyle", "Aspect_OpenVRSession", "Aspect_PolygonOffsetMode", "As<PERSON>_<PERSON>", "Aspect_SequenceOfColor", "Aspect_SkydomeBackground", "Aspect_Touch", "Aspect_TouchMap", "Aspect_TrackedDevicePose", "Aspect_TrackedDevicePoseArray", "Aspect_TypeOfColorScaleData", "Aspect_TypeOfColorScaleOrientation", "Aspect_TypeOfColorScalePosition", "Aspect_TypeOfDeflection", "Aspect_TypeOfDisplayText", "Aspect_TypeOfFacingModel", "Aspect_TypeOfHighlightMethod", "Aspect_TypeOfLine", "Aspect_TypeOfMarker", "Aspect_TypeOfResize", "Aspect_TypeOfStyleText", "Aspect_TypeOfTriedronPosition", "Aspect_VKeyBasic", "Aspect_VKeySet", "Aspect_WidthOfLine", "Aspect_Window", "Aspect_WindowInputListener", "Aspect_XAtom", "Aspect_XRAction", "Aspect_XRActionMap", "Aspect_XRActionSet", "Aspect_XRActionSetMap", "Aspect_XRActionType", "Aspect_XRAnalogActionData", "Aspect_XRDigitalActionData", "Aspect_XRGenericAction", "Aspect_XRHapticActionData", "Aspect_XRPoseActionData", "Aspect_XRSession", "Aspect_XRTrackedDeviceRole", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BOPAlgo", "type": "module", "classes": ["BOPAlgo_Algo", "BOPAlgo_ArgumentAnalyzer", "BOPAlgo_BOP", "BOPAlgo_Builder", "BOPAlgo_BuilderArea", "BOPAlgo_BuilderFace", "BOPAlgo_BuilderShape", "BOPAlgo_BuilderSolid", "BOPAlgo_CellsBuilder", "BOPAlgo_CheckResult", "BOPAlgo_CheckStatus", "BOPAlgo_CheckerSI", "BOPAlgo_GlueEnum", "BOPAlgo_ListOfCheckResult", "BOPAlgo_MakeConnected", "BOPAlgo_MakePeriodic", "BOPAlgo_MakerVolume", "BOPAlgo_Operation", "BOPAlgo_Options", "BOPAlgo_PISteps", "BOPAlgo_ParallelAlgo", "BOPAlgo_PaveFiller", "BOPAlgo_RemoveFeatures", "BOPAlgo_Section", "BOPAlgo_SectionAttribute", "BOPAlgo_ShellSplitter", "BOPAlgo_Splitter", "BOPAlgo_Tools", "BOPAlgo_ToolsProvider", "BOPAlgo_WireEdgeSet", "BOPAlgo_WireSplitter", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BOPDS", "type": "module", "classes": ["BOPDS_CommonBlock", "BOPDS_CoupleOfPaveBlocks", "BOPDS_Curve", "BOPDS_DS", "BOPDS_DataMapOfIntegerListOfPaveBlock", "BOPDS_DataMapOfPaveBlockListOfInteger", "BOPDS_DataMapOfPaveBlockListOfPaveBlock", "BOPDS_DataMapOfShapeCoupleOfPaveBlocks", "BOPDS_FaceInfo", "BOPDS_IndexRange", "BOPDS_IndexedDataMapOfPaveBlockListOfInteger", "BOPDS_IndexedDataMapOfPaveBlockListOfPaveBlock", "BOPDS_IndexedDataMapOfShapeCoupleOfPaveBlocks", "BOPDS_IndexedMapOfPaveBlock", "BOPDS_InterfEE", "BOPDS_InterfEF", "BOPDS_InterfEZ", "BOPDS_InterfFF", "BOPDS_InterfFZ", "BOPDS_InterfVE", "BOPDS_InterfVF", "BOPDS_InterfVV", "BOPDS_InterfVZ", "BOPDS_InterfZZ", "BOPDS_Iterator", "BOPDS_IteratorSI", "BOPDS_ListOfPave", "BOPDS_ListOfPaveBlock", "BOPDS_MapOfCommonBlock", "BOPDS_MapOfPair", "BOPDS_MapOfPave", "BOPDS_MapOfPaveBlock", "BOPDS_Pair", "BOPDS_Pave", "BOPDS_PaveBlock", "BOPDS_Point", "BOPDS_ShapeInfo", "BOPDS_SubIterator", "BOPDS_Tools", "BOPDS_VectorOfPave", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BOPTools", "type": "module", "classes": ["BOPTools_AlgoTools", "BOPTools_AlgoTools2D", "BOPTools_AlgoTools3D", "BOPTools_ConnexityBlock", "BOPTools_CoupleOfShape", "BOPTools_IndexedDataMapOfSetShape", "BOPTools_ListOfConnexityBlock", "BOPTools_ListOfCoupleOfShape", "BOPTools_MapOfSet", "BOPTools_Set", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRep", "type": "module", "classes": ["BRep_Builder", "BRep_Curve3D", "BRep_CurveOn2Surfaces", "BRep_CurveOnClosedSurface", "BRep_CurveOnSurface", "BRep_CurveRepresentation", "BRep_GCurve", "BRep_ListIteratorOfListOfCurveRepresentation", "BRep_ListIteratorOfListOfPointRepresentation", "BRep_ListOfCurveRepresentation", "BRep_ListOfPointRepresentation", "BRep_PointOnCurve", "BRep_PointOnCurveOnSurface", "BRep_PointOnSurface", "BRep_PointRepresentation", "BRep_PointsOnSurface", "BRep_Polygon3D", "BRep_PolygonOnClosedSurface", "BRep_PolygonOnClosedTriangulation", "BRep_PolygonOnSurface", "BRep_PolygonOnTriangulation", "BRep_TEdge", "BRep_TFace", "BRep_TVertex", "BRep_Tool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepAdaptor", "type": "module", "classes": ["BRepAdaptor_Array1OfCurve", "BRepAdaptor_CompCurve", "BRepAdaptor_Curve", "BRepAdaptor_Curve2d", "BRepAdaptor_HArray1OfCurve", "BRepAdaptor_Surface", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepAlgo", "type": "module", "classes": ["BRepAlgo_AsDes", "BRepAlgo_FaceRestrictor", "BRepAlgo_Image", "BRepAlgo_Loop", "BRepAlgo_NormalProjection", "SwigPyIterator", "_SwigNonDynamicMeta", "brepalgo", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepAlgoAPI", "type": "module", "classes": ["BRepAlgoAPI_Algo", "BRepAlgoAPI_BooleanOperation", "BRepAlgoAPI_BuilderAlgo", "BRepAlgoAPI_Check", "BRepAlgoAPI_Common", "BRepAlgoAPI_Cut", "BRepAlgoAPI_Defeaturing", "BRepAlgoAPI_Fuse", "BRepAlgoAPI_Section", "BRepAlgoAPI_Splitter", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepApprox", "type": "module", "classes": ["BRepApprox_Approx", "BRepApprox_ApproxLine", "BRepApprox_BSpGradient_BFGSOfMyBSplGradientOfTheComputeLineOfApprox", "BRepApprox_BSpParFunctionOfMyBSplGradientOfTheComputeLineOfApprox", "BRepApprox_BSpParLeastSquareOfMyBSplGradientOfTheComputeLineOfApprox", "BRepApprox_Gradient_BFGSOfMyGradientOfTheComputeLineBezierOfApprox", "BRepApprox_Gradient_BFGSOfMyGradientbisOfTheComputeLineOfApprox", "BRepApprox_MyBSplGradientOfTheComputeLineOfApprox", "BRepApprox_MyGradientOfTheComputeLineBezierOfApprox", "BRepApprox_MyGradientbisOfTheComputeLineOfApprox", "BRepApprox_ParFunctionOfMyGradientOfTheComputeLineBezierOfApprox", "BRepApprox_ParFunctionOfMyGradientbisOfTheComputeLineOfApprox", "BRepApprox_ParLeastSquareOfMyGradientOfTheComputeLineBezierOfApprox", "BRepApprox_ParLeastSquareOfMyGradientbisOfTheComputeLineOfApprox", "BRepApprox_ResConstraintOfMyGradientOfTheComputeLineBezierOfApprox", "BRepApprox_ResConstraintOfMyGradientbisOfTheComputeLineOfApprox", "BRepApprox_SurfaceTool", "BRepApprox_TheComputeLineBezierOfApprox", "BRepApprox_TheComputeLineOfApprox", "BRepApprox_TheFunctionOfTheInt2SOfThePrmPrmSvSurfacesOfApprox", "BRepApprox_TheImpPrmSvSurfacesOfApprox", "BRepApprox_TheInt2SOfThePrmPrmSvSurfacesOfApprox", "BRepApprox_TheMultiLineOfApprox", "BRepApprox_TheMultiLineToolOfApprox", "BRepApprox_ThePrmPrmSvSurfacesOfApprox", "BRepApprox_TheZerImpFuncOfTheImpPrmSvSurfacesOfApprox", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepBlend", "type": "module", "classes": ["BRepBlend_AppFunc", "BRepBlend_AppFuncRoot", "BRepBlend_AppFuncRst", "BRepBlend_AppFuncRstRst", "BRepBlend_AppSurf", "BRepBlend_AppSurface", "BRepBlend_BlendTool", "BRepBlend_CurvPointRadInv", "BRepBlend_Extremity", "BRepBlend_HCurve2dTool", "BRepBlend_HCurveTool", "BRepBlend_Line", "BRepBlend_PointOnRst", "BRepBlend_RstRstConstRad", "BRepBlend_RstRstEvolRad", "BRepBlend_RstRstLineBuilder", "BRepBlend_SequenceOfLine", "BRepBlend_SequenceOfPointOnRst", "BRepBlend_SurfCurvConstRadInv", "BRepBlend_SurfCurvEvolRadInv", "BRepBlend_SurfPointConstRadInv", "BRepBlend_SurfPointEvolRadInv", "BRepBlend_SurfRstConstRad", "BRepBlend_SurfRstEvolRad", "BRepBlend_SurfRstLineBuilder", "BRepBlend_Walking", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepBndLib", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "br<PERSON><PERSON><PERSON><PERSON><PERSON>", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepBuilderAPI", "type": "module", "classes": ["BRepBuilderAPI_BndBoxTree", "BRepBuilderAPI_BndBoxTreeSelector", "BRepBuilderAPI_Collect", "BRepBuilderAPI_Command", "BRepBuilderAPI_Copy", "BRepBuilderAPI_EdgeError", "BRepBuilderAPI_FaceError", "BRepBuilderAPI_FastSewing", "BRepBuilderAPI_FindPlane", "BRepBuilderAPI_GTransform", "BRepBuilderAPI_MakeEdge", "BRepBuilderAPI_MakeEdge2d", "BRepBuilderAPI_MakeFace", "BRepBuilderAPI_MakePolygon", "BRepBuilderAPI_MakeShape", "BRepBuilderAPI_MakeShapeOnMesh", "BRepBuilderAPI_MakeShell", "BRepBuilderAPI_MakeSolid", "BRepBuilderAPI_MakeVertex", "BRepBuilderAPI_MakeWire", "BRepBuilderAPI_ModifyShape", "BRepBuilderAPI_NurbsConvert", "BRepBuilderAPI_PipeError", "BRepBuilderAPI_Sewing", "BRepBuilderAPI_ShapeModification", "BRepBuilderAPI_ShellError", "BRepBuilderAPI_Transform", "BRepBuilderAPI_TransitionMode", "BRepBuilderAPI_VertexInspector", "BRepBuilderAPI_WireError", "SwigPyIterator", "_SwigNonDynamicMeta", "br<PERSON><PERSON><PERSON><PERSON><PERSON>", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepCheck", "type": "module", "classes": ["BRepCheck_Analyzer", "BRepCheck_Edge", "BRepCheck_Face", "BRepCheck_IndexedDataMapOfShapeResult", "BRepCheck_ListIteratorOfListOfStatus", "BRepCheck_ListOfStatus", "BRepCheck_Result", "BRepCheck_Shell", "BRepCheck_Solid", "BRepCheck_Status", "BRepCheck_Vertex", "BRepCheck_Wire", "SwigPyIterator", "_SwigNonDynamicMeta", "brepcheck", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepClass", "type": "module", "classes": ["BRepClass_Edge", "BRepClass_FClass2dOfFClassifier", "BRepClass_FClassifier", "BRepClass_FaceClassifier", "BRepClass_FaceExplorer", "BRepClass_FacePassiveClassifier", "BRepClass_Intersector", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepClass3d", "type": "module", "classes": ["BRepClass3d_BndBoxTree", "BRepClass3d_Intersector3d", "BRepClass3d_MapOfInter", "BRepClass3d_SClassifier", "BRepClass3d_SolidClassifier", "BRepClass3d_SolidExplorer", "BRepClass3d_SolidPassiveClassifier", "SwigPyIterator", "_SwigNonDynamicMeta", "brepclass3d", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepExtrema", "type": "module", "classes": ["BRepExtrema_DistShapeShape", "BRepExtrema_DistanceSS", "BRepExtrema_ElementFilter", "BRepExtrema_ExtCC", "BRepExtrema_ExtCF", "BRepExtrema_ExtFF", "BRepExtrema_ExtPC", "BRepExtrema_ExtPF", "BRepExtrema_MapOfIntegerPackedMapOfInteger", "BRepExtrema_Poly", "BRepExtrema_ProximityDistTool", "BRepExtrema_SelfIntersection", "BRepExtrema_SeqOfSolution", "BRepExtrema_ShapeProximity", "BRepExtrema_SolutionElem", "BRepExtrema_SupportType", "BRepExtrema_TriangleSet", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepFeat", "type": "module", "classes": ["BRepFeat_Builder", "BRepFeat_Form", "BRepFeat_Gluer", "BRepFeat_MakeCylindricalHole", "BRepFeat_MakeDPrism", "BRepFeat_MakeLinearForm", "BRepFeat_MakePipe", "BRepFeat_MakePrism", "BRepFeat_MakeRevol", "BRepFeat_MakeRevolutionForm", "BRepFeat_PerfSelection", "BRepFeat_RibSlot", "BRepFeat_SplitShape", "BRepFeat_Status", "BRepFeat_StatusError", "SwigPyIterator", "_SwigNonDynamicMeta", "brepfeat", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepFill", "type": "module", "classes": ["BRepFill_ACRLaw", "BRepFill_AdvancedEvolved", "BRepFill_ApproxSeewing", "BRepFill_CompatibleWires", "BRepFill_ComputeCLine", "BRepFill_CurveConstraint", "BRepFill_DataMapOfNodeDataMapOfShapeShape", "BRepFill_DataMapOfNodeShape", "BRepFill_DataMapOfOrientedShapeListOfShape", "BRepFill_DataMapOfShapeDataMapOfShapeListOfShape", "BRepFill_DataMapOfShapeHArray2OfShape", "BRepFill_DataMapOfShapeSequenceOfPnt", "BRepFill_DataMapOfShapeSequenceOfReal", "BRepFill_Draft", "BRepFill_DraftLaw", "BRepFill_Edge3DLaw", "BRepFill_EdgeFaceAndOrder", "BRepFill_EdgeOnSurfLaw", "BRepFill_Evolved", "BRepFill_FaceAndOrder", "BRepFill_Filling", "BRepFill_Generator", "BRepFill_IndexedDataMapOfOrientedShapeListOfShape", "BRepFill_ListIteratorOfListOfOffsetWire", "BRepFill_ListOfOffsetWire", "BRepFill_LocationLaw", "BRepFill_MultiLine", "BRepFill_NSections", "BRepFill_OffsetAncestors", "BRepFill_OffsetWire", "BRepFill_Pipe", "BRepFill_PipeShell", "BRepFill_Section", "BRepFill_SectionLaw", "BRepFill_SectionPlacement", "BRepFill_SequenceOfEdgeFaceAndOrder", "BRepFill_SequenceOfFaceAndOrder", "BRepFill_SequenceOfSection", "BRepFill_ShapeLaw", "BRepFill_Sweep", "BRepFill_ThruSectionErrorStatus", "BRepFill_TransitionStyle", "BRepFill_TrimEdgeTool", "BRepFill_TrimShellCorner", "BRepFill_TrimSurfaceTool", "BRepFill_TypeOfContact", "SwigPyIterator", "_SwigNonDynamicMeta", "brepfill", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepFilletAPI", "type": "module", "classes": ["BRepFilletAPI_LocalOperation", "BRepFilletAPI_MakeChamfer", "BRepFilletAPI_MakeFillet", "BRepFilletAPI_MakeFillet2d", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepGProp", "type": "module", "classes": ["BRepGProp_Cinert", "BRepGProp_Domain", "BRepGProp_EdgeTool", "BRepGProp_Face", "BRepGProp_Gauss", "BRepGProp_MeshCinert", "BRepGProp_Sinert", "BRepGProp_TFunction", "BRepGProp_UFunction", "BRepGProp_Vinert", "BRepGProp_VinertGK", "SwigPyIterator", "_SwigNonDynamicMeta", "brepgprop", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepIntCurveSurface", "type": "module", "classes": ["BRepIntCurveSurface_Inter", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepLProp", "type": "module", "classes": ["BRepLProp_CLProps", "BRepLProp_CurveTool", "BRepLProp_SLProps", "BRepLProp_SurfaceTool", "SwigPyIterator", "_SwigNonDynamicMeta", "breplprop", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepLib", "type": "module", "classes": ["BRepLib_CheckCurveOnSurface", "BRepLib_Command", "BRepLib_EdgeError", "BRepLib_FaceError", "BRepLib_FindSurface", "BRepLib_FuseEdges", "BRepLib_MakeEdge", "BRepLib_MakeEdge2d", "BRepLib_MakeFace", "BRepLib_MakePolygon", "BRepLib_MakeShape", "BRepLib_MakeShell", "BRepLib_MakeSolid", "BRepLib_MakeVertex", "BRepLib_MakeWire", "BRepLib_PointCloudShape", "BRepLib_ShapeModification", "BRepLib_ShellError", "BRepLib_ToolTriangulatedShape", "BRepLib_ValidateEdge", "BRepLib_WireError", "SwigPyIterator", "_SwigNonDynamicMeta", "breplib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepMAT2d", "type": "module", "classes": ["BRepMAT2d_BisectingLocus", "BRepMAT2d_DataMapOfBasicEltShape", "BRepMAT2d_DataMapOfShapeSequenceOfBasicElt", "BRepMAT2d_Explorer", "BRepMAT2d_LinkTopoBilo", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepMesh", "type": "module", "classes": ["BRepMesh_BaseMeshAlgo", "BRepMesh_BoundaryParamsRangeSplitter", "BRepMesh_Circle", "BRepMesh_CircleInspector", "BRepMesh_CircleTool", "BRepMesh_Classifier", "BRepMesh_ConeRangeSplitter", "BRepMesh_Context", "BRepMesh_CurveTessellator", "BRepMesh_CylinderRangeSplitter", "BRepMesh_DataStructureOfDelaun", "BRepMesh_DefaultRangeSplitter", "BRepMesh_Deflection", "BRepMesh_DegreeOfFreedom", "BRepMesh_DelabellaBaseMeshAlgo", "BRepMesh_DelabellaMeshAlgoFactory", "BRepMesh_Delaun", "BRepMesh_DelaunayBaseMeshAlgo", "BRepMesh_DiscretFactory", "BRepMesh_DiscretRoot", "BRepMesh_Edge", "BRepMesh_EdgeDiscret", "BRepMesh_ExtrusionRangeSplitter", "BRepMesh_FaceChecker", "BRepMesh_FaceDiscret", "BRepMesh_FactoryError", "BRepMesh_GeomTool", "BRepMesh_IncrementalMesh", "BRepMesh_MeshAlgoFactory", "BRepMesh_MeshTool", "BRepMesh_ModelBuilder", "BRepMesh_ModelHealer", "BRepMesh_ModelPostProcessor", "BRepMesh_ModelPreProcessor", "BRepMesh_NURBSRangeSplitter", "BRepMesh_OrientedEdge", "BRepMesh_PairOfIndex", "BRepMesh_SelectorOfDataStructureOfDelaun", "BRepMesh_ShapeTool", "BRepMesh_ShapeVisitor", "BRepMesh_SphereRangeSplitter", "BRepMesh_TorusRangeSplitter", "BRepMesh_Triangle", "BRepMesh_Triangulator", "BRepMesh_UVParamRangeSplitter", "BRepMesh_UndefinedRangeSplitter", "BRepMesh_Vertex", "BRepMesh_VertexInspector", "BRepMesh_VertexTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepMeshData", "type": "module", "classes": ["BRepMeshData_Curve", "BRepMeshData_Edge", "BRepMeshData_Face", "BRepMeshData_Model", "BRepMeshData_PCurve", "BRepMeshData_Wire", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepOffset", "type": "module", "classes": ["BRepOffsetSimple_Status", "BRepOffset_Analyse", "BRepOffset_DataMapOfShapeListOfInterval", "BRepOffset_DataMapOfShapeMapOfShape", "BRepOffset_DataMapOfShapeOffset", "BRepOffset_Error", "BRepOffset_Inter2d", "BRepOffset_Inter3d", "BRepOffset_Interval", "BRepOffset_ListIteratorOfListOfInterval", "BRepOffset_ListOfInterval", "BRepOffset_MakeLoops", "BRepOffset_MakeOffset", "BRepOffset_MakeSimpleOffset", "BRepOffset_Mode", "BRepOffset_Offset", "BRepOffset_SimpleOffset", "BRepOffset_Status", "BRepOffset_Tool", "SwigPyIterator", "_SwigNonDynamicMeta", "brepoffset", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepOffsetAPI", "type": "module", "classes": ["BRepOffsetAPI_DraftAngle", "BRepOffsetAPI_FindContigousEdges", "BRepOffsetAPI_MakeDraft", "BRepOffsetAPI_MakeEvolved", "BRepOffsetAPI_MakeFilling", "BRepOffsetAPI_MakeOffset", "BRepOffsetAPI_MakeOffsetShape", "BRepOffsetAPI_MakePipe", "BRepOffsetAPI_MakePipeShell", "BRepOffsetAPI_MakeThickSolid", "BRepOffsetAPI_MiddlePath", "BRepOffsetAPI_NormalProjection", "BRepOffsetAPI_SequenceOfSequenceOfReal", "BRepOffsetAPI_SequenceOfSequenceOfShape", "BRepOffsetAPI_ThruSections", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepPrim", "type": "module", "classes": ["BRepPrim_Builder", "BRepPrim_Cone", "BRepPrim_Cylinder", "BRepPrim_Direction", "BRepPrim_FaceBuilder", "BRepPrim_GWedge", "BRepPrim_OneAxis", "BRepPrim_Revolution", "BRepPrim_Sphere", "BRepPrim_Torus", "BRepPrim_Wedge", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepPrimAPI", "type": "module", "classes": ["BRepPrimAPI_MakeBox", "BRepPrimAPI_MakeCone", "BRepPrimAPI_MakeCylinder", "BRepPrimAPI_MakeHalfSpace", "BRepPrimAPI_MakeOneAxis", "BRepPrimAPI_MakePrism", "BRepPrimAPI_MakeRevol", "BRepPrimAPI_MakeRevolution", "BRepPrimAPI_MakeSphere", "BRepPrimAPI_MakeSweep", "BRepPrimAPI_MakeTorus", "BRepPrimAPI_MakeWedge", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepProj", "type": "module", "classes": ["BRepProj_Projection", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepSweep", "type": "module", "classes": ["BRepSweep_Builder", "BRepSweep_Iterator", "BRepSweep_NumLinearRegularSweep", "BRepSweep_Prism", "BRepSweep_Revol", "BRepSweep_Rotation", "BRepSweep_Tool", "BRepSweep_Translation", "BRepSweep_Trsf", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepTools", "type": "module", "classes": ["BRepTools_CopyModification", "BRepTools_GTrsfModification", "BRepTools_History", "BRepTools_MapOfVertexPnt2d", "BRepTools_Modification", "BRepTools_Modifier", "BRepTools_NurbsConvertModification", "BRepTools_PurgeLocations", "BRepTools_Quilt", "BRepTools_ReShape", "BRepTools_ShapeSet", "BRepTools_Substitution", "BRepTools_TrsfModification", "BRepTools_WireExplorer", "SwigPyIterator", "_SwigNonDynamicMeta", "breptools", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BRepTopAdaptor", "type": "module", "classes": ["BRepTopAdaptor_FClass2d", "BRepTopAdaptor_HVertex", "BRepTopAdaptor_MapOfShapeTool", "BRepTopAdaptor_Tool", "BRepTopAdaptor_TopolTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BSplCLib", "type": "module", "classes": ["BSplCLib_Cache", "BSplCLib_CacheParams", "BSplCLib_EvaluatorFunction", "BSplCLib_KnotDistribution", "BSplCLib_MultDistribution", "SwigPyIterator", "_SwigNonDynamicMeta", "bsplclib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BSplSLib", "type": "module", "classes": ["BSplSLib_Cache", "BSplSLib_EvaluatorFunction", "SwigPyIterator", "_SwigNonDynamicMeta", "bsplslib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BVH", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BiTgte", "type": "module", "classes": ["BiTgte_Blend", "BiTgte_ContactType", "BiTgte_CurveOnEdge", "BiTgte_CurveOnVertex", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinDrivers", "type": "module", "classes": ["BinDrivers_DocumentRetrievalDriver", "BinDrivers_DocumentStorageDriver", "BinDrivers_Marker", "SwigPyIterator", "_SwigNonDynamicMeta", "bindrivers", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinLDrivers", "type": "module", "classes": ["BinLDrivers_DocumentRetrievalDriver", "BinLDrivers_DocumentSection", "BinLDrivers_DocumentStorageDriver", "BinLDrivers_Marker", "SwigPyIterator", "_SwigNonDynamicMeta", "binldrivers", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMDF", "type": "module", "classes": ["BinMDF_ADriver", "BinMDF_ADriverTable", "BinMDF_DerivedDriver", "BinMDF_ReferenceDriver", "BinMDF_TagSourceDriver", "BinMDF_TypeADriverMap", "BinMDF_TypeIdMap", "SwigPyIterator", "_SwigNonDynamicMeta", "binmdf", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMDataStd", "type": "module", "classes": ["BinMDataStd_AsciiStringDriver", "BinMDataStd_BooleanArrayDriver", "BinMDataStd_BooleanListDriver", "BinMDataStd_ByteArrayDriver", "BinMDataStd_ExpressionDriver", "BinMDataStd_ExtStringArrayDriver", "BinMDataStd_ExtStringListDriver", "BinMDataStd_GenericEmptyDriver", "BinMDataStd_GenericExtStringDriver", "BinMDataStd_IntPackedMapDriver", "BinMDataStd_IntegerArrayDriver", "BinMDataStd_IntegerDriver", "BinMDataStd_IntegerListDriver", "BinMDataStd_NamedDataDriver", "BinMDataStd_RealArrayDriver", "BinMDataStd_RealDriver", "BinMDataStd_RealListDriver", "BinMDataStd_ReferenceArrayDriver", "BinMDataStd_ReferenceListDriver", "BinMDataStd_TreeNodeDriver", "BinMDataStd_UAttributeDriver", "BinMDataStd_VariableDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmdatastd", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMDataXtd", "type": "module", "classes": ["BinMDataXtd_ConstraintDriver", "BinMDataXtd_GeometryDriver", "BinMDataXtd_PatternStdDriver", "BinMDataXtd_PositionDriver", "BinMDataXtd_PresentationDriver", "BinMDataXtd_TriangulationDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmdataxtd", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMDocStd", "type": "module", "classes": ["BinMDocStd_XLinkDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmdocstd", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMFunction", "type": "module", "classes": ["BinMFunction_FunctionDriver", "BinMFunction_GraphNodeDriver", "BinMFunction_ScopeDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmfunction", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMNaming", "type": "module", "classes": ["BinMNaming_NamedShapeDriver", "BinMNaming_NamingDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmnaming", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinMXCAFDoc", "type": "module", "classes": ["BinMXCAFDoc_AssemblyItemRefDriver", "BinMXCAFDoc_CentroidDriver", "BinMXCAFDoc_ColorDriver", "BinMXCAFDoc_DatumDriver", "BinMXCAFDoc_DimTolDriver", "BinMXCAFDoc_GraphNodeDriver", "BinMXCAFDoc_LengthUnitDriver", "BinMXCAFDoc_LocationDriver", "BinMXCAFDoc_MaterialDriver", "BinMXCAFDoc_NoteBinDataDriver", "BinMXCAFDoc_NoteCommentDriver", "BinMXCAFDoc_NoteDriver", "BinMXCAFDoc_VisMaterialDriver", "BinMXCAFDoc_VisMaterialToolDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binmxcafdoc", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinObjMgt", "type": "module", "classes": ["BinObjMgt_Persistent", "BinObjMgt_RRelocationTable", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinTObjDrivers", "type": "module", "classes": ["BinTObjDrivers_DocumentRetrievalDriver", "BinTObjDrivers_DocumentStorageDriver", "BinTObjDrivers_IntSparseArrayDriver", "BinTObjDrivers_ModelDriver", "BinTObjDrivers_ObjectDriver", "BinTObjDrivers_ReferenceDriver", "BinTObjDrivers_XYZDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "bintobjdrivers", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinTools", "type": "module", "classes": ["BinTools_Curve2dSet", "BinTools_CurveSet", "BinTools_FormatVersion", "BinTools_LocationSet", "BinTools_ObjectType", "BinTools_ShapeReader", "BinTools_ShapeSet", "BinTools_ShapeSetBase", "BinTools_ShapeWriter", "BinTools_SurfaceSet", "SwigPyIterator", "_SwigNonDynamicMeta", "bintools", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BinXCAFDrivers", "type": "module", "classes": ["BinXCAFDrivers_DocumentRetrievalDriver", "BinXCAFDrivers_DocumentStorageDriver", "SwigPyIterator", "_SwigNonDynamicMeta", "binxcafdrivers", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Bisector", "type": "module", "classes": ["B<PERSON><PERSON>_<PERSON><PERSON>c", "Bisector_BisecAna", "Bisector_BisecCC", "Bisector_BisecPC", "Bisector_Curve", "Bisector_FunctionH", "Bisector_FunctionInter", "Bisector_Inter", "Bisector_PointOnBis", "Bisector_PolyBis", "SwigPyIterator", "_SwigNonDynamicMeta", "bisector", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Blend", "type": "module", "classes": ["Blend_AppFunction", "Blend_CSFunction", "Blend_CurvPointFuncInv", "Blend_DecrochStatus", "Blend_FuncInv", "Blend_Function", "Blend_Point", "Blend_RstRstFunction", "Blend_SequenceOfPoint", "Blend_Status", "Blend_SurfCurvFuncInv", "Blend_SurfPointFuncInv", "Blend_SurfRstFunction", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BlendFunc", "type": "module", "classes": ["BlendFunc_CSCircular", "BlendFunc_CSConstRad", "BlendFunc_ChAsym", "BlendFunc_ChAsymInv", "BlendFunc_ChamfInv", "BlendFunc_Chamfer", "BlendFunc_ConstRad", "BlendFunc_ConstRadInv", "BlendFunc_ConstThroat", "BlendFunc_ConstThroatInv", "BlendFunc_ConstThroatWithPenetration", "BlendFunc_ConstThroatWithPenetrationInv", "BlendFunc_Corde", "BlendFunc_EvolRad", "BlendFunc_EvolRadInv", "BlendFunc_GenChamfInv", "BlendFunc_GenChamfer", "BlendFunc_Ruled", "BlendFunc_RuledInv", "BlendFunc_SectionShape", "BlendFunc_Tensor", "SwigPyIterator", "_SwigNonDynamicMeta", "blendfunc", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Bnd", "type": "module", "classes": ["Bnd_Array1OfBox", "Bnd_Array1OfBox2d", "Bnd_Array1OfSphere", "Bnd_B2d", "Bnd_B2f", "Bnd_B3d", "Bnd_B3f", "Bnd_BoundSortBox", "Bnd_Box", "Bnd_Box2d", "Bnd_HArray1OfBox", "Bnd_HArray1OfBox2d", "Bnd_HArray1OfSphere", "Bnd_OBB", "Bnd_Range", "Bnd_Sphere", "Bnd_Tools", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.BndLib", "type": "module", "classes": ["BndLib_Add2dCurve", "BndLib_Add3dCurve", "BndLib_AddSurface", "SwigPyIterator", "_SwigNonDynamicMeta", "bndlib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.CDF", "type": "module", "classes": ["CDF_Application", "CDF_Directory", "CDF_FWOSDriver", "CDF_MetaDataDriver", "CDF_MetaDataDriverFactory", "CDF_Store", "CDF_StoreList", "CDF_StoreSetNameStatus", "CDF_SubComponentStatus", "CDF_TryStoreStatus", "CDF_TypeOfActivation", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.CDM", "type": "module", "classes": ["CDM_Application", "CDM_CanCloseStatus", "CDM_Document", "CDM_ListIteratorOfListOfDocument", "CDM_ListIteratorOfListOfReferences", "CDM_ListOfDocument", "CDM_ListOfReferences", "CDM_MapOfDocument", "CDM_MetaData", "CDM_MetaDataLookUpTable", "CDM_Reference", "CDM_ReferenceIterator", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.CPnts", "type": "module", "classes": ["CPnts_AbscissaPoint", "CPnts_MyGaussFunction", "CPnts_MyRootFunction", "CPnts_UniformDeflection", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.CSLib", "type": "module", "classes": ["CSLib_Class2d", "CSLib_DerivativeStatus", "CSLib_NormalPolyDef", "CSLib_NormalStatus", "SwigPyIterator", "_SwigNonDynamicMeta", "cslib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ChFi2d", "type": "module", "classes": ["ChFi2d_AnaFilletAlgo", "ChFi2d_Builder", "ChFi2d_ChamferAPI", "ChFi2d_ConstructionError", "ChFi2d_FilletAPI", "ChFi2d_FilletAlgo", "SwigPyIterator", "_SwigNonDynamicMeta", "chfi2d", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ChFi3d", "type": "module", "classes": ["ChFi3d_Builder", "ChFi3d_ChBuilder", "ChFi3d_FilBuilder", "ChFi3d_FilletShape", "ChFi3d_SearchSing", "SwigPyIterator", "_SwigNonDynamicMeta", "chfi3d", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ChFiDS", "type": "module", "classes": ["ChFiDS_ChamfMethod", "ChFiDS_ChamfMode", "ChFiDS_ChamfSpine", "ChFiDS_CircSection", "ChFiDS_CommonPoint", "ChFiDS_ElSpine", "ChFiDS_ErrorStatus", "ChFiDS_FaceInterference", "ChFiDS_FilSpine", "ChFiDS_HData", "ChFiDS_IndexedDataMapOfVertexListOfStripe", "ChFiDS_ListIteratorOfListOfHElSpine", "ChFiDS_ListIteratorOfListOfStripe", "ChFiDS_ListIteratorOfRegularities", "ChFiDS_ListOfHElSpine", "ChFiDS_ListOfStripe", "ChFiDS_Map", "ChFiDS_Regul", "ChFiDS_Regularities", "ChFiDS_SecArray1", "ChFiDS_SecHArray1", "ChFiDS_SequenceOfSpine", "ChFiDS_SequenceOfSurfData", "ChFiDS_Spine", "ChFiDS_State", "ChFiDS_Stripe", "ChFiDS_StripeArray1", "ChFiDS_StripeMap", "ChFiDS_SurfData", "ChFiDS_TypeOfConcavity", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ChFiKPart", "type": "module", "classes": ["ChFiKPart_ComputeData", "ChFiKPart_RstMap", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Contap", "type": "module", "classes": ["Contap_ArcFunction", "Contap_ContAna", "Contap_Contour", "Contap_HContTool", "Contap_HCurve2dTool", "Contap_IType", "Contap_Line", "Contap_Point", "Contap_SequenceOfIWLineOfTheIWalking", "Contap_SequenceOfPathPointOfTheSearch", "Contap_SequenceOfSegmentOfTheSearch", "Contap_SurfFunction", "Contap_SurfProps", "Contap_TFunction", "Contap_TheHSequenceOfPoint", "Contap_TheIWLineOfTheIWalking", "Contap_TheIWalking", "Contap_ThePathPointOfTheSearch", "Contap_TheSearch", "Contap_TheSearchInside", "Contap_TheSegmentOfTheSearch", "Contap_TheSequenceOfLine", "Contap_TheSequenceOfPoint", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Convert", "type": "module", "classes": ["Convert_CircleToBSplineCurve", "Convert_CompBezierCurves2dToBSplineCurve2d", "Convert_CompBezierCurvesToBSplineCurve", "Convert_CompPolynomialToPoles", "Convert_ConeToBSplineSurface", "Convert_ConicToBSplineCurve", "Convert_CylinderToBSplineSurface", "Convert_ElementarySurfaceToBSplineSurface", "Convert_EllipseToBSplineCurve", "Convert_GridPolynomialToPoles", "Convert_HyperbolaToBSplineCurve", "Convert_ParabolaToBSplineCurve", "Convert_ParameterisationType", "Convert_SequenceOfArray1OfPoles", "Convert_SphereToBSplineSurface", "Convert_TorusToBSplineSurface", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.DE", "type": "module", "classes": ["DE_ConfigurationContext", "DE_ConfigurationFormatMap", "DE_ConfigurationNode", "DE_ConfigurationVendorMap", "DE_Provider", "DE_Wrapper", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.DEBRepCascade", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.DEXCAFCascade", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Draft", "type": "module", "classes": ["Draft_EdgeInfo", "Draft_ErrorStatus", "Draft_FaceInfo", "Draft_IndexedDataMapOfEdgeEdgeInfo", "Draft_IndexedDataMapOfFaceFaceInfo", "Draft_IndexedDataMapOfVertexVertexInfo", "Draft_Modification", "Draft_VertexInfo", "SwigPyIterator", "_SwigNonDynamicMeta", "draft", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.DsgPrs", "type": "module", "classes": ["DsgPrs_ArrowSide", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ElCLib", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "elclib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ElSLib", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "el<PERSON><PERSON><PERSON>", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Exception", "type": "module", "classes": ["ClassNotWrappedError", "MethodNotWrappedError"], "children": []}, {"name": "OCC.Core.Expr", "type": "module", "classes": ["Expr_Array1OfGeneralExpression", "Expr_Array1OfNamedUnknown", "Expr_Array1OfSingleRelation", "Expr_MapOfNamedUnknown", "Expr_SequenceOfGeneralExpression", "Expr_SequenceOfGeneralRelation", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ExprIntrp", "type": "module", "classes": ["ExprIntrp_ListIteratorOfStackOfGeneralExpression", "ExprIntrp_ListIteratorOfStackOfGeneralRelation", "ExprIntrp_SequenceOfNamedExpression", "ExprIntrp_StackOfGeneralExpression", "ExprIntrp_StackOfGeneralRelation", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Extrema", "type": "module", "classes": ["Extrema_Array1OfPOnCurv", "Extrema_Array1OfPOnCurv2d", "Extrema_Array1OfPOnSurf", "Extrema_CCLocFOfLocECC", "Extrema_CCLocFOfLocECC2d", "Extrema_Curve2dTool", "Extrema_CurveTool", "Extrema_ECC", "Extrema_ECC2d", "Extrema_ELPCOfLocateExtPC", "Extrema_ELPCOfLocateExtPC2d", "Extrema_EPCOfELPCOfLocateExtPC", "Extrema_EPCOfELPCOfLocateExtPC2d", "Extrema_EPCOfExtPC", "Extrema_EPCOfExtPC2d", "Extrema_ElementType", "Extrema_ExtAlgo", "Extrema_ExtCC", "Extrema_ExtCC2d", "Extrema_ExtCS", "Extrema_ExtElC", "Extrema_ExtElC2d", "Extrema_ExtElCS", "Extrema_ExtElSS", "Extrema_ExtFlag", "Extrema_ExtPC", "Extrema_ExtPC2d", "Extrema_ExtPElC", "Extrema_ExtPElC2d", "Extrema_ExtPElS", "Extrema_ExtPExtS", "Extrema_ExtPRevS", "Extrema_ExtPS", "Extrema_ExtSS", "Extrema_FuncExtCS", "Extrema_FuncExtSS", "Extrema_FuncPSDist", "Extrema_FuncPSNorm", "Extrema_GenExtCS", "Extrema_GenExtPS", "Extrema_GenExtSS", "Extrema_GenLocateExtCS", "Extrema_GenLocateExtPS", "Extrema_GenLocateExtSS", "Extrema_GlobOptFuncCCC0", "Extrema_GlobOptFuncCCC1", "Extrema_GlobOptFuncCCC2", "Extrema_GlobOptFuncCQuadric", "Extrema_GlobOptFuncCS", "Extrema_GlobOptFuncConicS", "Extrema_HArray1OfPOnCurv", "Extrema_HArray1OfPOnCurv2d", "Extrema_HArray1OfPOnSurf", "Extrema_HArray2OfPOnCurv", "Extrema_HArray2OfPOnCurv2d", "Extrema_HArray2OfPOnSurf", "Extrema_LocECC", "Extrema_LocECC2d", "Extrema_LocEPCOfLocateExtPC", "Extrema_LocEPCOfLocateExtPC2d", "Extrema_LocateExtCC", "Extrema_LocateExtCC2d", "Extrema_LocateExtPC", "Extrema_LocateExtPC2d", "Extrema_PCFOfEPCOfELPCOfLocateExtPC", "Extrema_PCFOfEPCOfELPCOfLocateExtPC2d", "Extrema_PCFOfEPCOfExtPC", "Extrema_PCFOfEPCOfExtPC2d", "Extrema_PCLocFOfLocEPCOfLocateExtPC", "Extrema_PCLocFOfLocEPCOfLocateExtPC2d", "Extrema_POnCurv", "Extrema_POnCurv2d", "Extrema_POnSurf", "Extrema_POnSurfParams", "Extrema_SequenceOfPOnCurv", "Extrema_SequenceOfPOnCurv2d", "Extrema_SequenceOfPOnSurf", "Extrema_UBTreeFillerOfSphere", "Extrema_UBTreeOfSphere", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.FEmTool", "type": "module", "classes": ["FEmTool_HAssemblyTable", "FEmTool_ListIteratorOfListOfVectors", "FEmTool_ListOfVectors", "FEmTool_SeqOfLinConstr", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.FSD", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.FairCurve", "type": "module", "classes": ["FairCurve_AnalysisCode", "Fair<PERSON>ur<PERSON>_Batten", "FairCurve_BattenLaw", "FairCurve_DistributionOfEnergy", "FairCurve_DistributionOfJerk", "FairCurve_DistributionOfSagging", "FairCurve_DistributionOfTension", "FairCurve_Energy", "FairCurve_EnergyOfBatten", "FairCurve_EnergyOfMVC", "FairCurve_MinimalVariation", "FairCurve_Newton", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.FilletSurf", "type": "module", "classes": ["FilletSurf_Builder", "FilletSurf_ErrorTypeStatus", "FilletSurf_InternalBuilder", "FilletSurf_StatusDone", "FilletSurf_StatusType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GC", "type": "module", "classes": ["GC_MakeArcOfCircle", "GC_MakeArcOfEllipse", "GC_MakeArcOfHyperbola", "GC_MakeArcOfParabola", "GC_MakeCircle", "GC_MakeConicalSurface", "GC_MakeCylindricalSurface", "G<PERSON>_<PERSON><PERSON>pse", "GC_MakeHyperbola", "GC_MakeLine", "GC_MakeMirror", "GC_MakePlane", "GC_MakeRotation", "GC_MakeScale", "GC_MakeSegment", "GC_MakeTranslation", "GC_MakeTrimmedCone", "GC_MakeTrimmedCylinder", "GC_Root", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GCE2d", "type": "module", "classes": ["GCE2d_MakeArcOfCircle", "GCE2d_MakeArcOfEllipse", "GCE2d_MakeArcOfHyperbola", "GCE2d_MakeArcOfParabola", "GCE2d_MakeCircle", "GCE2d_MakeEllipse", "GCE2d_MakeHyperbola", "GCE2d_MakeLine", "GCE2d_MakeMirror", "GCE2d_MakeParabola", "GCE2d_MakeRotation", "GCE2d_MakeScale", "GCE2d_MakeSegment", "GCE2d_MakeTranslation", "GCE2d_Root", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GCPnts", "type": "module", "classes": ["GCPnts_AbscissaPoint", "GCPnts_AbscissaType", "GCPnts_DeflectionType", "GCPnts_DistFunction2dMV", "GCPnts_DistFunctionMV", "GCPnts_QuasiUniformAbscissa", "GCPnts_QuasiUniformDeflection", "GCPnts_TangentialDeflection", "GCPnts_UniformAbscissa", "GCPnts_UniformDeflection", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GProp", "type": "module", "classes": ["GProp_CelGProps", "GProp_EquaType", "GProp_GProps", "GProp_PEquation", "GProp_PGProps", "GProp_PrincipalProps", "GProp_SelGProps", "GProp_ValueType", "GProp_VelGProps", "SwigPyIterator", "_SwigNonDynamicMeta", "gprop", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GccAna", "type": "module", "classes": ["GccAna_Circ2d2TanOn", "GccAna_Circ2d2TanRad", "GccAna_Circ2d3Tan", "GccAna_Circ2dBisec", "GccAna_Circ2dTanCen", "GccAna_Circ2dTanOnRad", "GccAna_CircLin2dBisec", "GccAna_CircPnt2dBisec", "GccAna_Lin2d2Tan", "GccAna_Lin2dBisec", "GccAna_Lin2dTanObl", "GccAna_Lin2dTanPar", "GccAna_Lin2dTanPer", "GccAna_LinPnt2dBisec", "GccAna_Pnt2dBisec", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GccEnt", "type": "module", "classes": ["GccEnt_Position", "GccEnt_QualifiedCirc", "GccEnt_QualifiedLin", "SwigPyIterator", "_SwigNonDynamicMeta", "gccent", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GccInt", "type": "module", "classes": ["GccInt_BCirc", "GccInt_BElips", "GccInt_BHyper", "GccInt_BLine", "GccInt_BParab", "GccInt_BPoint", "GccInt_Bisec", "GccInt_IType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom", "type": "module", "classes": ["Geom_Axis1Placement", "Geom_Axis2Placement", "Geom_AxisPlacement", "Geom_BSplineCurve", "Geom_BSplineSurface", "Geom_BezierCurve", "Geom_BezierSurface", "Geom_BoundedCurve", "Geom_BoundedSurface", "Geom_CartesianPoint", "Geom_Circle", "Geom_Conic", "Geom_ConicalSurface", "Geom_Curve", "Geom_CylindricalSurface", "Geom_Direction", "Geom_ElementarySurface", "Geom_Ellipse", "Geom_Geometry", "Geom_HSequenceOfBSplineSurface", "Geom_Hyperbola", "Geom_Line", "Geom_OffsetCurve", "Geom_OffsetSurface", "Geom_OsculatingSurface", "Geom_Parabola", "Geom_Plane", "Geom_Point", "Geom_RectangularTrimmedSurface", "Geom_SequenceOfBSplineSurface", "Geom_SphericalSurface", "Geom_Surface", "Geom_SurfaceOfLinearExtrusion", "Geom_SurfaceOfRevolution", "Geom_SweptSurface", "Geom_ToroidalSurface", "Geom_Transformation", "Geom_TrimmedCurve", "Geom_Vector", "Geom_VectorWithMagnitude", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2d", "type": "module", "classes": ["Geom2d_AxisPlacement", "Geom2d_BSplineCurve", "Geom2d_BezierCurve", "Geom2d_BoundedCurve", "Geom2d_CartesianPoint", "Geom2d_Circle", "Geom2d_Conic", "Geom2d_Curve", "Geom2d_Direction", "Geom2d_Ellipse", "Geom2d_Geometry", "Geom2d_Hyperbola", "Geom2d_Line", "Geom2d_OffsetCurve", "Geom2d_Parabola", "Geom2d_Point", "Geom2d_Transformation", "Geom2d_TrimmedCurve", "Geom2d_Vector", "Geom2d_VectorWithMagnitude", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dAPI", "type": "module", "classes": ["Geom2dAPI_ExtremaCurveCurve", "Geom2dAPI_InterCurveCurve", "Geom2dAPI_Interpolate", "Geom2dAPI_PointsToBSpline", "Geom2dAPI_ProjectPointOnCurve", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dAdaptor", "type": "module", "classes": ["Geom2dAdaptor_Curve", "SwigPyIterator", "_SwigNonDynamicMeta", "geom2dadaptor", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dConvert", "type": "module", "classes": ["Geom2dConvert_ApproxArcsSegments", "Geom2dConvert_ApproxCurve", "Geom2dConvert_BSplineCurveKnotSplitting", "Geom2dConvert_BSplineCurveToBezierCurve", "Geom2dConvert_CompCurveToBSplineCurve", "Geom2dConvert_PPoint", "Geom2dConvert_SequenceOfPPoint", "SwigPyIterator", "_SwigNonDynamicMeta", "geom2dconvert", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dEvaluator", "type": "module", "classes": ["Geom2dEvaluator_Curve", "Geom2dEvaluator_OffsetCurve", "SwigPyIterator", "_SwigNonDynamicMeta", "geom2devaluator", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dGcc", "type": "module", "classes": ["Geom2dGcc_Circ2d2TanOn", "Geom2dGcc_Circ2d2TanOnGeo", "Geom2dGcc_Circ2d2TanOnIter", "Geom2dGcc_Circ2d2TanRad", "Geom2dGcc_Circ2d2TanRadGeo", "Geom2dGcc_Circ2d3Tan", "Geom2dGcc_Circ2d3TanIter", "Geom2dGcc_Circ2dTanCen", "Geom2dGcc_Circ2dTanCenGeo", "Geom2dGcc_Circ2dTanOnRad", "Geom2dGcc_Circ2dTanOnRadGeo", "Geom2dGcc_CurveTool", "Geom2dGcc_FunctionTanCirCu", "Geom2dGcc_FunctionTanCuCu", "Geom2dGcc_FunctionTanCuCuOnCu", "Geom2dGcc_FunctionTanCuPnt", "Geom2dGcc_FunctionTanObl", "Geom2dGcc_Lin2d2Tan", "Geom2dGcc_Lin2d2TanIter", "Geom2dGcc_Lin2dTanObl", "Geom2dGcc_Lin2dTanOblIter", "Geom2dGcc_QCurve", "Geom2dGcc_QualifiedCurve", "Geom2dGcc_Type1", "Geom2dGcc_Type2", "Geom2dGcc_Type3", "SwigPyIterator", "_SwigNonDynamicMeta", "geom2dgcc", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dHatch", "type": "module", "classes": ["Geom2dHatch_Classifier", "Geom2dHatch_Element", "Geom2dHatch_Elements", "Geom2dHatch_FClass2dOfClassifier", "Geom2dHatch_Hatcher", "Geom2dHatch_Hatching", "Geom2dHatch_Hatchings", "Geom2dHatch_Intersector", "Geom2dHatch_MapOfElements", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dInt", "type": "module", "classes": ["Geom2dInt_ExactIntersectionPointOfTheIntPCurvePCurveOfGInter", "Geom2dInt_GInter", "Geom2dInt_Geom2dCurveTool", "Geom2dInt_IntConicCurveOfGInter", "Geom2dInt_MyImpParToolOfTheIntersectorOfTheIntConicCurveOfGInter", "Geom2dInt_PCLocFOfTheLocateExtPCOfTheProjPCurOfGInter", "Geom2dInt_TheCurveLocatorOfTheProjPCurOfGInter", "Geom2dInt_TheDistBetweenPCurvesOfTheIntPCurvePCurveOfGInter", "Geom2dInt_TheIntConicCurveOfGInter", "Geom2dInt_TheIntPCurvePCurveOfGInter", "Geom2dInt_TheIntersectorOfTheIntConicCurveOfGInter", "Geom2dInt_TheLocateExtPCOfTheProjPCurOfGInter", "Geom2dInt_ThePolygon2dOfTheIntPCurvePCurveOfGInter", "Geom2dInt_TheProjPCurOfGInter", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Geom2dLProp", "type": "module", "classes": ["Geom2dLProp_CLProps2d", "Geom2dLProp_CurAndInf2d", "Geom2dLProp_Curve2dTool", "Geom2dLProp_FuncCurExt", "Geom2dLProp_FuncCurNul", "Geom2dLProp_NumericCurInf2d", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomAPI", "type": "module", "classes": ["GeomAPI_ExtremaCurveCurve", "GeomAPI_ExtremaCurveSurface", "GeomAPI_ExtremaSurfaceSurface", "GeomAPI_IntCS", "GeomAPI_IntSS", "GeomAPI_Interpolate", "GeomAPI_PointsToBSpline", "GeomAPI_PointsToBSplineSurface", "GeomAPI_ProjectPointOnCurve", "GeomAPI_ProjectPointOnSurf", "SwigPyIterator", "_SwigNonDynamicMeta", "geomapi", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomAbs", "type": "module", "classes": ["GeomAbs_BSplKnotDistribution", "GeomAbs_CurveType", "GeomAbs_IsoType", "GeomAbs_JoinType", "GeomAbs_Shape", "GeomAbs_SurfaceType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomAdaptor", "type": "module", "classes": ["GeomAdaptor_Curve", "GeomAdaptor_Surface", "GeomAdaptor_SurfaceOfLinearExtrusion", "GeomAdaptor_SurfaceOfRevolution", "SwigPyIterator", "_SwigNonDynamicMeta", "geomadaptor", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomConvert", "type": "module", "classes": ["GeomConvert_ApproxCurve", "GeomConvert_ApproxSurface", "GeomConvert_BSplineCurveKnotSplitting", "GeomConvert_BSplineCurveToBezierCurve", "GeomConvert_BSplineSurfaceKnotSplitting", "GeomConvert_BSplineSurfaceToBezierSurface", "GeomConvert_CompBezierSurfacesToBSplineSurface", "GeomConvert_CompCurveToBSplineCurve", "GeomConvert_ConvType", "GeomConvert_CurveToAnaCurve", "GeomConvert_FuncConeLSDist", "GeomConvert_FuncCylinderLSDist", "GeomConvert_FuncSphereLSDist", "GeomConvert_SurfToAnaSurf", "GeomConvert_Units", "SwigPyIterator", "_SwigNonDynamicMeta", "geomconvert", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomEvaluator", "type": "module", "classes": ["GeomEvaluator_Curve", "GeomEvaluator_OffsetCurve", "GeomEvaluator_OffsetSurface", "GeomEvaluator_Surface", "GeomEvaluator_SurfaceOfExtrusion", "GeomEvaluator_SurfaceOfRevolution", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomFill", "type": "module", "classes": ["GeomFill_AppSurf", "GeomFill_AppSweep", "GeomFill_ApproxStyle", "GeomFill_Array1OfLocationLaw", "GeomFill_Array1OfSectionLaw", "GeomFill_BSplineCurves", "GeomFill_BezierCurves", "GeomFill_BoundWithSurf", "GeomFill_Boundary", "GeomFill_CircularBlendFunc", "GeomFill_ConstantBiNormal", "GeomFill_ConstrainedFilling", "Geom<PERSON>ill_Coons", "GeomFill_CoonsAlgPatch", "GeomFill_CornerState", "GeomFill_CorrectedFrenet", "GeomFill_CurveAndTrihedron", "GeomFill_Curved", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GeomFill_DegeneratedBound", "GeomFill_DiscreteTrihedron", "GeomFill_DraftTrihedron", "GeomFill_EvolvedSection", "GeomFill_Filling", "GeomFill_FillingStyle", "GeomFill_Fixed", "GeomFill_Fr<PERSON>", "GeomFill_FunctionDraft", "GeomFill_FunctionGuide", "GeomFill_Generator", "GeomFill_GuideTrihedronAC", "GeomFill_GuideTrihedronPlan", "GeomFill_HArray1OfLocationLaw", "GeomFill_HArray1OfSectionLaw", "GeomFill_HSequenceOfAx2", "GeomFill_Line", "GeomFill_LocFunction", "GeomFill_LocationDraft", "GeomFill_LocationGuide", "GeomFill_LocationLaw", "GeomFill_NSections", "GeomFill_Pipe", "GeomFill_PipeError", "GeomFill_PlanFunc", "GeomFill_PolynomialConvertor", "GeomFill_Profiler", "GeomFill_QuasiAngularConvertor", "GeomFill_SectionGenerator", "GeomFill_SectionLaw", "GeomFill_SectionPlacement", "GeomFill_SequenceOfAx2", "GeomFill_SequenceOfTrsf", "GeomFill_SimpleBound", "GeomFill_SnglrFunc", "GeomFill_Stretch", "GeomFill_Sweep", "GeomFill_SweepFunction", "GeomFill_Tensor", "GeomFill_TgtField", "GeomFill_TgtOnCoons", "GeomFill_Trihedron", "GeomFill_TrihedronLaw", "GeomFill_TrihedronWithGuide", "GeomFill_UniformSection", "SwigPyIterator", "_SwigNonDynamicMeta", "geomfill", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomInt", "type": "module", "classes": ["GeomInt_BSpGradient_BFGSOfMyBSplGradientOfTheComputeLineOfWLApprox", "GeomInt_BSpParFunctionOfMyBSplGradientOfTheComputeLineOfWLApprox", "GeomInt_BSpParLeastSquareOfMyBSplGradientOfTheComputeLineOfWLApprox", "GeomInt_Gradient_BFGSOfMyGradientOfTheComputeLineBezierOfWLApprox", "GeomInt_Gradient_BFGSOfMyGradientbisOfTheComputeLineOfWLApprox", "GeomInt_IntSS", "GeomInt_LineConstructor", "GeomInt_LineTool", "GeomInt_MyBSplGradientOfTheComputeLineOfWLApprox", "GeomInt_MyGradientOfTheComputeLineBezierOfWLApprox", "GeomInt_MyGradientbisOfTheComputeLineOfWLApprox", "GeomInt_ParFunctionOfMyGradientOfTheComputeLineBezierOfWLApprox", "GeomInt_ParFunctionOfMyGradientbisOfTheComputeLineOfWLApprox", "GeomInt_ParLeastSquareOfMyGradientOfTheComputeLineBezierOfWLApprox", "GeomInt_ParLeastSquareOfMyGradientbisOfTheComputeLineOfWLApprox", "GeomInt_ParameterAndOrientation", "GeomInt_ResConstraintOfMyGradientOfTheComputeLineBezierOfWLApprox", "GeomInt_ResConstraintOfMyGradientbisOfTheComputeLineOfWLApprox", "GeomInt_SequenceOfParameterAndOrientation", "GeomInt_TheComputeLineBezierOfWLApprox", "GeomInt_TheComputeLineOfWLApprox", "GeomInt_TheFunctionOfTheInt2SOfThePrmPrmSvSurfacesOfWLApprox", "GeomInt_TheImpPrmSvSurfacesOfWLApprox", "GeomInt_TheInt2SOfThePrmPrmSvSurfacesOfWLApprox", "GeomInt_TheMultiLineOfWLApprox", "GeomInt_TheMultiLineToolOfWLApprox", "GeomInt_ThePrmPrmSvSurfacesOfWLApprox", "GeomInt_TheZerImpFuncOfTheImpPrmSvSurfacesOfWLApprox", "SwigPyIterator", "_SwigNonDynamicMeta", "geomint", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomLProp", "type": "module", "classes": ["GeomLProp_CLProps", "GeomLProp_CurveTool", "GeomLProp_SLProps", "GeomLProp_SurfaceTool", "SwigPyIterator", "_SwigNonDynamicMeta", "geomlprop", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomLib", "type": "module", "classes": ["GeomLib_Array1OfMat", "GeomLib_Check2dBSplineCurve", "GeomLib_CheckBSplineCurve", "GeomLib_CheckCurveOnSurface", "GeomLib_DenominatorMultiplier", "GeomLib_Interpolate", "GeomLib_InterpolationErrors", "GeomLib_IsPlanarSurface", "GeomLib_LogSample", "GeomLib_MakeCurvefromApprox", "GeomLib_PolyFunc", "GeomLib_Tool", "SwigPyIterator", "_SwigNonDynamicMeta", "geomlib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomPlate", "type": "module", "classes": ["GeomPlate_Aij", "GeomPlate_Array1OfHCurve", "GeomPlate_Array1OfSequenceOfReal", "GeomPlate_BuildAveragePlane", "GeomPlate_BuildPlateSurface", "GeomPlate_CurveConstraint", "GeomPlate_HArray1OfHCurve", "GeomPlate_HArray1OfSequenceOfReal", "GeomPlate_HSequenceOfCurveConstraint", "GeomPlate_HSequenceOfPointConstraint", "GeomPlate_MakeApprox", "GeomPlate_PlateG0Criterion", "GeomPlate_PlateG1Criterion", "GeomPlate_PointConstraint", "GeomPlate_SequenceOfAij", "GeomPlate_SequenceOfCurveConstraint", "GeomPlate_SequenceOfPointConstraint", "GeomPlate_Surface", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomProjLib", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "geomprojlib", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomToStep", "type": "module", "classes": ["GeomToStep_MakeAxis1Placement", "GeomToStep_MakeAxis2Placement2d", "GeomToStep_MakeAxis2Placement3d", "GeomToStep_MakeBSplineCurveWithKnots", "GeomToStep_MakeBSplineCurveWithKnotsAndRationalBSplineCurve", "GeomToStep_MakeBSplineSurfaceWithKnots", "GeomToStep_MakeBSplineSurfaceWithKnotsAndRationalBSplineSurface", "GeomToStep_MakeBoundedCurve", "GeomToStep_MakeBoundedSurface", "GeomToStep_MakeCartesianPoint", "GeomToStep_MakeCircle", "GeomToStep_MakeConic", "GeomToStep_MakeConicalSurface", "GeomToStep_MakeCurve", "GeomToStep_MakeCylindricalSurface", "GeomToStep_MakeDirection", "GeomToStep_MakeElementarySurface", "GeomToStep_MakeEllipse", "GeomToStep_MakeHyperbola", "GeomToStep_MakeLine", "GeomToStep_MakeParabola", "GeomToStep_MakePlane", "GeomToStep_MakePolyline", "GeomToStep_MakeRectangularTrimmedSurface", "GeomToStep_MakeSphericalSurface", "GeomToStep_MakeSurface", "GeomToStep_MakeSurfaceOfLinearExtrusion", "GeomToStep_MakeSurfaceOfRevolution", "GeomToStep_MakeSweptSurface", "GeomToStep_MakeToroidalSurface", "GeomToStep_MakeVector", "GeomToStep_Root", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.GeomTools", "type": "module", "classes": ["GeomTools_Curve2dSet", "GeomTools_CurveSet", "GeomTools_SurfaceSet", "GeomTools_UndefinedTypeHandler", "SwigPyIterator", "_SwigNonDynamicMeta", "geomtools", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Graphic3d", "type": "module", "classes": ["Graphic3d_AlphaMode", "Graphic3d_Array1OfAttribute", "Graphic3d_ArrayOfIndexedMapOfStructure", "Graphic3d_ArrayOfPoints", "Graphic3d_ArrayOfPolygons", "Graphic3d_ArrayOfPolylines", "Graphic3d_ArrayOfPrimitives", "Graphic3d_ArrayOfQuadrangleStrips", "Graphic3d_ArrayOfQuadrangles", "Graphic3d_ArrayOfSegments", "Graphic3d_ArrayOfTriangleFans", "Graphic3d_ArrayOfTriangleStrips", "Graphic3d_ArrayOfTriangles", "Graphic3d_AspectFillArea3d", "Graphic3d_AspectLine3d", "Graphic3d_AspectMarker3d", "Graphic3d_AspectText3d", "Graphic3d_Aspects", "Graphic3d_AttribBuffer", "Graphic3d_Attribute", "Graphic3d_BSDF", "Graphic3d_BoundBuffer", "Graphic3d_BufferRange", "Graphic3d_BufferType", "Graphic3d_BvhCStructureSet", "Graphic3d_CLight", "Graphic3d_CStructure", "Graphic3d_CView", "Graphic3d_Camera", "Graphic3d_CameraLerp", "Graphic3d_CameraTile", "Graphic3d_CappingFlags", "Graphic3d_ClipPlane", "Graphic3d_ClipState", "Graphic3d_CubeMap", "Graphic3d_CubeMapSeparate", "Graphic3d_CubeMapSide", "Graphic3d_CullingTool", "Graphic3d_DataStructureManager", "Graphic3d_DiagnosticInfo", "Graphic3d_DisplayPriority", "Graphic3d_FrameStats", "Graphic3d_FrameStatsCounter", "Graphic3d_FrameStatsData", "Graphic3d_FrameStatsDataTmp", "Graphic3d_FrameStatsTimer", "Graphic3d_Fresnel", "Graphic3d_FresnelModel", "Graphic3d_GlslExtension", "Graphic3d_GraduatedTrihedron", "Graphic3d_GraphicDriver", "Graphic3d_GraphicDriverFactory", "Graphic3d_GraphicDriverFactoryList", "Graphic3d_Group", "Graphic3d_GroupAspect", "Graphic3d_HatchStyle", "Graphic3d_HorizontalTextAlignment", "Graphic3d_IndexedMapOfStructure", "Graphic3d_IndexedMapOfView", "Graphic3d_Layer", "Graphic3d_LevelOfTextureAnisotropy", "Graphic3d_LightSet", "Graphic3d_MapOfAspectsToAspects", "Graphic3d_MapOfStructure", "Graphic3d_MarkerImage", "Graphic3d_Mat4", "Graphic3d_Mat4d", "Graphic3d_MaterialAspect", "Graphic3d_MediaTexture", "Graphic3d_MediaTextureSet", "Graphic3d_MutableIndexBuffer", "Graphic3d_NameOfMaterial", "Graphic3d_NameOfTexture1D", "Graphic3d_NameOfTexture2D", "Graphic3d_NameOfTextureEnv", "Graphic3d_NameOfTexturePlane", "Graphic3d_PBRMaterial", "Graphic3d_PolygonOffset", "Graphic3d_PresentationAttributes", "Graphic3d_RenderTransparentMethod", "Graphic3d_RenderingMode", "Graphic3d_RenderingParams", "Graphic3d_SequenceOfGroup", "Graphic3d_SequenceOfHClipPlane", "Graphic3d_SequenceOfStructure", "Graphic3d_ShaderAttribute", "Graphic3d_ShaderAttributeList", "Graphic3d_ShaderFlags", "Graphic3d_ShaderManager", "Graphic3d_ShaderObject", "Graphic3d_ShaderObjectList", "Graphic3d_ShaderProgram", "Graphic3d_ShaderVariable", "Graphic3d_ShaderVariableList", "Graphic3d_StereoMode", "Graphic3d_Structure", "Graphic3d_StructureManager", "Graphic3d_Text", "Graphic3d_TextPath", "Graphic3d_Texture1D", "Graphic3d_Texture1Dmanual", "Graphic3d_Texture1Dsegment", "Graphic3d_Texture2D", "Graphic3d_Texture2Dmanual", "Graphic3d_Texture2Dplane", "Graphic3d_Texture3D", "Graphic3d_TextureEnv", "Graphic3d_TextureMap", "Graphic3d_TextureParams", "Graphic3d_TextureRoot", "Graphic3d_TextureSetBits", "Graphic3d_TextureUnit", "Graphic3d_ToneMappingMethod", "Graphic3d_TransModeFlags", "Graphic3d_TransformPers", "Graphic3d_TransformPersScaledAbove", "Graphic3d_TypeOfAnswer", "Graphic3d_TypeOfAttribute", "Graphic3d_TypeOfBackfacingModel", "Graphic3d_TypeOfBackground", "Graphic3d_TypeOfConnection", "Graphic3d_TypeOfData", "Graphic3d_TypeOfLightSource", "Graphic3d_TypeOfLimit", "Graphic3d_TypeOfMaterial", "Graphic3d_TypeOfPrimitiveArray", "Graphic3d_TypeOfReflection", "Graphic3d_TypeOfShaderObject", "Graphic3d_TypeOfShadingModel", "Graphic3d_TypeOfStructure", "Graphic3d_TypeOfTexture", "Graphic3d_TypeOfTextureFilter", "Graphic3d_TypeOfTextureMode", "Graphic3d_TypeOfVisualization", "Graphic3d_ValidatedCubeMapOrder", "Graphic3d_ValueInterface", "Graphic3d_Vec2b", "Graphic3d_Vec2d", "Graphic3d_Vec2i", "Graphic3d_Vec2ub", "Graphic3d_Vec3b", "Graphic3d_Vec3d", "Graphic3d_Vec3i", "Graphic3d_Vec3ub", "Graphic3d_Vec4", "Graphic3d_Vec4b", "Graphic3d_Vec4d", "Graphic3d_Vec4i", "Graphic3d_Vec4ub", "Graphic3d_Vertex", "Graphic3d_VerticalTextAlignment", "Graphic3d_ViewAffinity", "Graphic3d_WorldViewProjState", "Graphic3d_ZLayerSettings", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HLRAlgo", "type": "module", "classes": ["HLRAlgo_Array1OfPHDat", "HLRAlgo_Array1OfPINod", "HLRAlgo_Array1OfPISeg", "HLRAlgo_Array1OfTData", "HLRAlgo_BiPoint", "HLRAlgo_Coincidence", "HLRAlgo_EdgeIterator", "HLRAlgo_EdgeStatus", "HLRAlgo_EdgesBlock", "HLRAlgo_HArray1OfPHDat", "HLRAlgo_HArray1OfPINod", "HLRAlgo_HArray1OfPISeg", "HLRAlgo_HArray1OfTData", "HLRAlgo_Interference", "HLRAlgo_InterferenceList", "HLRAlgo_Intersection", "HLRAlgo_ListIteratorOfInterferenceList", "HLRAlgo_ListIteratorOfListOfBPoint", "HLRAlgo_ListOfBPoint", "HLRAlgo_PolyAlgo", "HLRAlgo_PolyData", "HLRAlgo_PolyInternalData", "HLRAlgo_PolyInternalNode", "HLRAlgo_PolyMask", "HLRAlgo_PolyShellData", "HLRAlgo_Projector", "HLRAlgo_WiresBlock", "SwigPyIterator", "_SwigNonDynamicMeta", "hlralgo", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HLRAppli", "type": "module", "classes": ["HLRAppli_ReflectLines", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HLRBRep", "type": "module", "classes": ["HLRBRep_Algo", "HLRBRep_AreaLimit", "HLRBRep_Array1OfEData", "HLRBRep_Array1OfFData", "HLRBRep_BCurveTool", "HLRBRep_BiPnt2D", "HLRBRep_BiPoint", "HLRBRep_CInter", "HLRBRep_CLProps", "HLRBRep_CLPropsATool", "HLRBRep_Curve", "HLRBRep_CurveTool", "HLRBRep_Data", "HLRBRep_EdgeBuilder", "HLRBRep_EdgeData", "HLRBRep_EdgeFaceTool", "HLRBRep_EdgeIList", "HLRBRep_EdgeInterferenceTool", "HLRBRep_ExactIntersectionPointOfTheIntPCurvePCurveOfCInter", "HLRBRep_FaceData", "HLRBRep_FaceIterator", "HLRBRep_HLRToShape", "HLRBRep_Hider", "HLRBRep_IntConicCurveOfCInter", "HLRBRep_InterCSurf", "HLRBRep_InternalAlgo", "HLRBRep_Intersector", "HLRBRep_LineTool", "HLRBRep_ListIteratorOfListOfBPnt2D", "HLRBRep_ListIteratorOfListOfBPoint", "HLRBRep_ListOfBPnt2D", "HLRBRep_ListOfBPoint", "HLRBRep_MyImpParToolOfTheIntersectorOfTheIntConicCurveOfCInter", "HLRBRep_PCLocFOfTheLocateExtPCOfTheProjPCurOfCInter", "HLRBRep_PolyAlgo", "HLRBRep_PolyHLRToShape", "HLRBRep_SLProps", "HLRBRep_SLPropsATool", "HLRBRep_SeqOfShapeBounds", "HLRBRep_ShapeBounds", "HLRBRep_ShapeToHLR", "HLRBRep_SurfaceTool", "HLRBRep_TheCSFunctionOfInterCSurf", "HLRBRep_TheDistBetweenPCurvesOfTheIntPCurvePCurveOfCInter", "HLRBRep_TheExactInterCSurf", "HLRBRep_TheIntConicCurveOfCInter", "HLRBRep_TheIntPCurvePCurveOfCInter", "HLRBRep_TheInterferenceOfInterCSurf", "HLRBRep_TheIntersectorOfTheIntConicCurveOfCInter", "HLRBRep_TheLocateExtPCOfTheProjPCurOfCInter", "HLRBRep_ThePolygon2dOfTheIntPCurvePCurveOfCInter", "HLRBRep_ThePolygonOfInterCSurf", "HLRBRep_ThePolygonToolOfInterCSurf", "HLRBRep_ThePolyhedronToolOfInterCSurf", "HLRBRep_TheProjPCurOfCInter", "HLRBRep_TheQuadCurvExactInterCSurf", "HLRBRep_TheQuadCurvFuncOfTheQuadCurvExactInterCSurf", "HLRBRep_TypeOfResultingEdge", "HLRBRep_VertexList", "SwigPyIterator", "_SwigNonDynamicMeta", "hlrbrep", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HLRTopoBRep", "type": "module", "classes": ["HLRTopoBRep_DSFiller", "HLRTopoBRep_Data", "HLRTopoBRep_DataMapOfShapeFaceData", "HLRTopoBRep_FaceData", "HLRTopoBRep_FaceIsoLiner", "HLRTopoBRep_ListIteratorOfListOfVData", "HLRTopoBRep_ListOfVData", "HLRTopoBRep_MapOfShapeListOfVData", "HLRTopoBRep_OutLiner", "HLRTopoBRep_VData", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Hatch", "type": "module", "classes": ["<PERSON><PERSON><PERSON><PERSON>", "Hatch_Line", "Hatch_LineForm", "Hatch_Parameter", "Hatch_SequenceOfLine", "Hatch_SequenceOfParameter", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HatchGen", "type": "module", "classes": ["HatchGen_Domain", "HatchGen_Domains", "HatchGen_ErrorStatus", "HatchGen_IntersectionPoint", "HatchGen_IntersectionType", "HatchGen_PointOnElement", "HatchGen_PointOnHatching", "HatchGen_PointsOnElement", "HatchGen_PointsOnHatching", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.HeaderSection", "type": "module", "classes": ["HeaderSection_FileDescription", "HeaderSection_FileName", "HeaderSection_FileSchema", "HeaderSection_Protocol", "SwigPyIterator", "_SwigNonDynamicMeta", "headersection", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Hermit", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "hermit", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IFSelect", "type": "module", "classes": ["IFSelect_Act", "IFSelect_Activator", "IFSelect_AppliedModifiers", "IFSelect_BasicDumper", "IFSelect_CheckCounter", "IFSelect_ContextModif", "IFSelect_ContextWrite", "IFSelect_DispGlobal", "IFSelect_DispPerCount", "IFSelect_DispPerFiles", "IFSelect_DispPerOne", "IFSelect_DispPerSignature", "IFSelect_Dispatch", "IFSelect_EditForm", "IFSelect_EditValue", "IFSelect_Editor", "IFSelect_Functions", "IFSelect_GeneralModifier", "IFSelect_GraphCounter", "IFSelect_HSeqOfSelection", "IFSelect_IntParam", "IFSelect_ListEditor", "IFSelect_ModelCopier", "IFSelect_ModifEditForm", "IFSelect_ModifReorder", "IFSelect_Modifier", "IFSelect_PacketList", "IFSelect_ParamEditor", "IFSelect_PrintCount", "IFSelect_PrintFail", "IFSelect_RemainMode", "IFSelect_ReturnStatus", "IFSelect_SelectAnyList", "IFSelect_SelectAnyType", "IFSelect_SelectBase", "IFSelect_SelectCombine", "IFSelect_SelectControl", "IFSelect_SelectDeduct", "IFSelect_SelectDiff", "IFSelect_SelectEntityNumber", "IFSelect_SelectErrorEntities", "IFSelect_SelectExplore", "IFSelect_SelectExtract", "IFSelect_SelectFlag", "IFSelect_SelectInList", "IFSelect_SelectIncorrectEntities", "IFSelect_SelectIntersection", "IFSelect_SelectModelEntities", "IFSelect_SelectModelRoots", "IFSelect_SelectPointed", "IFSelect_SelectRange", "IFSelect_SelectRootComps", "IFSelect_SelectRoots", "IFSelect_SelectSent", "IFSelect_SelectShared", "IFSelect_SelectSharing", "IFSelect_SelectSignature", "IFSelect_SelectSignedShared", "IFSelect_SelectSignedSharing", "IFSelect_SelectSuite", "IFSelect_SelectType", "IFSelect_SelectUnion", "IFSelect_SelectUnknownEntities", "IFSelect_Selection", "IFSelect_SelectionIterator", "IFSelect_SequenceOfAppliedModifiers", "IFSelect_SequenceOfGeneralModifier", "IFSelect_SequenceOfInterfaceModel", "IFSelect_SessionDumper", "IFSelect_SessionFile", "IFSelect_SessionPilot", "IFSelect_ShareOut", "IFSelect_ShareOutResult", "IFSelect_SignAncestor", "IFSelect_SignCategory", "IFSelect_SignCounter", "IFSelect_SignMultiple", "IFSelect_SignType", "IFSelect_SignValidity", "IFSelect_Signature", "IFSelect_SignatureList", "IFSelect_TSeqOfDispatch", "IFSelect_TSeqOfSelection", "IFSelect_TransformStandard", "IFSelect_Transformer", "IFSelect_WorkLibrary", "IFSelect_WorkSession", "SwigPyIterator", "_SwigNonDynamicMeta", "ifselect", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IGESCAFControl", "type": "module", "classes": ["IGESCAFControl_Reader", "IGESCAFControl_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "igescafcontrol", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IGESControl", "type": "module", "classes": ["IGESControl_ActorWrite", "IGESControl_AlgoContainer", "IGESControl_Controller", "IGESControl_IGESBoundary", "IGESControl_Reader", "IGESControl_ToolContainer", "IGESControl_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IGESData", "type": "module", "classes": ["IGESData_Array1OfDirPart", "IGESData_Array1OfIGESEntity", "IGESData_BasicEditor", "IGESData_ColorEntity", "IGESData_DefList", "IGESData_DefSwitch", "IGESData_DefType", "IGESData_DefaultGeneral", "IGESData_DefaultSpecific", "IGESData_DirChecker", "IGESData_DirPart", "IGESData_FileProtocol", "IGESData_FileRecognizer", "IGESData_FreeFormatEntity", "IGESData_GeneralModule", "IGESData_GlobalNodeOfSpecificLib", "IGESData_GlobalNodeOfWriterLib", "IGESData_GlobalSection", "IGESData_HArray1OfIGESEntity", "IGESData_IGESDumper", "IGESData_IGESEntity", "IGESData_IGESModel", "IGESData_IGESReaderData", "IGESData_IGESReaderTool", "IGESData_IGESType", "IGESData_IGESWriter", "IGESData_LabelDisplayEntity", "IGESData_LevelListEntity", "IGESData_LineFontEntity", "IGESData_NameEntity", "IGESData_NodeOfSpecificLib", "IGESData_NodeOfWriterLib", "IGESData_ParamCursor", "IGESData_ParamReader", "IGESData_Protocol", "IGESData_ReadStage", "IGESData_ReadWriteModule", "IGESData_SingleParentEntity", "IGESData_SpecificLib", "IGESData_SpecificModule", "IGESData_Status", "IGESData_ToolLocation", "IGESData_TransfEntity", "IGESData_UndefinedEntity", "IGESData_ViewKindEntity", "IGESData_WriterLib", "SwigPyIterator", "_SwigNonDynamicMeta", "igesdata", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IGESToBRep", "type": "module", "classes": ["IGESToBRep_Actor", "IGESToBRep_AlgoContainer", "IGESToBRep_BRepEntity", "IGESToBRep_BasicCurve", "IGESToBRep_BasicSurface", "IGESToBRep_CurveAndSurface", "IGESToBRep_IGESBoundary", "IGESToBRep_Reader", "IGESToBRep_ToolContainer", "IGESToBRep_TopoCurve", "IGESToBRep_TopoSurface", "SwigPyIterator", "_SwigNonDynamicMeta", "igestobrep", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IMeshData", "type": "module", "classes": ["IMeshData_Curve", "IMeshData_Edge", "IMeshData_Face", "IMeshData_Model", "IMeshData_PCurve", "IMeshData_ParametersList", "IMeshData_Shape", "IMeshData_Status", "IMeshData_StatusOwner", "IMeshData_TessellatedShape", "IMeshData_Wire", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IMeshTools", "type": "module", "classes": ["IMeshTools_Context", "IMeshTools_CurveTessellator", "IMeshTools_MeshAlgo", "IMeshTools_MeshAlgoFactory", "IMeshTools_MeshAlgoType", "IMeshTools_MeshBuilder", "IMeshTools_ModelAlgo", "IMeshTools_ModelBuilder", "IMeshTools_Parameters", "IMeshTools_ShapeExplorer", "IMeshTools_ShapeVisitor", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Image", "type": "module", "classes": ["Image_CompressedFormat", "Image_Format", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntAna", "type": "module", "classes": ["IntAna_Curve", "IntAna_Int3Pln", "IntAna_IntConicQuad", "IntAna_IntLinTorus", "IntAna_IntQuadQuad", "IntAna_ListIteratorOfListOfCurve", "IntAna_ListOfCurve", "IntAna_QuadQuadGeo", "IntAna_Quadric", "IntAna_ResultType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntAna2d", "type": "module", "classes": ["IntAna2d_AnaIntersection", "IntAna2d_Conic", "IntAna2d_IntPoint", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntCurve", "type": "module", "classes": ["IntCurve_IConicTool", "IntCurve_IntConicConic", "IntCurve_IntImpConicParConic", "IntCurve_MyImpParToolOfIntImpConicParConic", "IntCurve_PConic", "IntCurve_PConicTool", "IntCurve_ProjectOnPConicTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntCurveSurface", "type": "module", "classes": ["IntCurveSurface_HInter", "IntCurveSurface_Intersection", "IntCurveSurface_IntersectionPoint", "IntCurveSurface_IntersectionSegment", "IntCurveSurface_SequenceOfPnt", "IntCurveSurface_SequenceOfSeg", "IntCurveSurface_TheCSFunctionOfHInter", "IntCurveSurface_TheExactHInter", "IntCurveSurface_TheHCurveTool", "IntCurveSurface_TheInterferenceOfHInter", "IntCurveSurface_ThePolygonOfHInter", "IntCurveSurface_ThePolygonToolOfHInter", "IntCurveSurface_ThePolyhedronToolOfHInter", "IntCurveSurface_TheQuadCurvExactHInter", "IntCurveSurface_TheQuadCurvFuncOfTheQuadCurvExactHInter", "IntCurveSurface_TransitionOnCurve", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntCurvesFace", "type": "module", "classes": ["IntCurvesFace_Intersector", "IntCurvesFace_ShapeIntersector", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntImp", "type": "module", "classes": ["IntImp_ConstIsoparametric", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntImpParGen", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "intimppargen", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntPatch", "type": "module", "classes": ["IntPatch_ALine", "IntPatch_ALineToWLine", "IntPatch_ArcFunction", "IntPatch_CSFunction", "IntPatch_CurvIntSurf", "IntPatch_GLine", "IntPatch_HCurve2dTool", "IntPatch_HInterTool", "IntPatch_IType", "IntPatch_ImpImpIntersection", "IntPatch_ImpPrmIntersection", "IntPatch_InterferencePolyhedron", "IntPatch_Intersection", "IntPatch_Line", "IntPatch_LineConstructor", "IntPatch_Point", "IntPatch_PointLine", "IntPatch_PolyArc", "IntPatch_PolyLine", "IntPatch_Polygo", "IntPatch_PolyhedronTool", "IntPatch_PrmPrmIntersection", "IntPatch_PrmPrmIntersection_T3Bits", "IntPatch_RLine", "IntPatch_RstInt", "IntPatch_SequenceOfIWLineOfTheIWalking", "IntPatch_SequenceOfLine", "IntPatch_SequenceOfPathPointOfTheSOnBounds", "IntPatch_SequenceOfPoint", "IntPatch_SequenceOfSegmentOfTheSOnBounds", "IntPatch_SpecPntType", "IntPatch_SpecialPoints", "IntPatch_TheIWLineOfTheIWalking", "IntPatch_TheIWalking", "IntPatch_ThePathPointOfTheSOnBounds", "IntPatch_TheSOnBounds", "IntPatch_TheSearchInside", "IntPatch_TheSegmentOfTheSOnBounds", "IntPatch_TheSurfFunction", "IntPatch_WLine", "IntPatch_WLineTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntPolyh", "type": "module", "classes": ["IntPolyh_Couple", "IntPolyh_Edge", "IntPolyh_Intersection", "IntPolyh_ListOfCouples", "IntPolyh_Point", "IntPolyh_PointNormal", "IntPolyh_SectionLine", "IntPolyh_SeqOfStartPoints", "IntPolyh_StartPoint", "IntPolyh_Tools", "IntPolyh_Triangle", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntRes2d", "type": "module", "classes": ["IntRes2d_Domain", "IntRes2d_Intersection", "IntRes2d_IntersectionPoint", "IntRes2d_IntersectionSegment", "IntRes2d_Position", "IntRes2d_SequenceOfIntersectionPoint", "IntRes2d_SequenceOfIntersectionSegment", "IntRes2d_Situation", "IntRes2d_Transition", "IntRes2d_TypeTrans", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntStart", "type": "module", "classes": ["IntStart_SITopolTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntSurf", "type": "module", "classes": ["IntSurf_Allocator", "IntSurf_Couple", "IntSurf_InteriorPoint", "IntSurf_InteriorPointTool", "IntSurf_LineOn2S", "IntSurf_ListIteratorOfListOfPntOn2S", "IntSurf_ListOfPntOn2S", "IntSurf_PathPoint", "IntSurf_PathPointTool", "IntSurf_PntOn2S", "IntSurf_Quadric", "IntSurf_QuadricTool", "IntSurf_SequenceOfCouple", "IntSurf_SequenceOfInteriorPoint", "IntSurf_SequenceOfPathPoint", "IntSurf_SequenceOfPntOn2S", "IntSurf_Situation", "IntSurf_Transition", "IntSurf_TypeTrans", "SwigPyIterator", "_SwigNonDynamicMeta", "intsurf", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntTools", "type": "module", "classes": ["IntTools_Array1OfRange", "IntTools_Array1OfRoots", "IntTools_BaseRangeSample", "IntTools_BeanFaceIntersector", "IntTools_CommonPrt", "IntTools_Context", "IntTools_Curve", "IntTools_CurveRangeLocalizeData", "IntTools_CurveRangeSample", "IntTools_DataMapOfCurveSampleBox", "IntTools_DataMapOfSurfaceSampleBox", "IntTools_EdgeEdge", "IntTools_EdgeFace", "IntTools_FClass2d", "IntTools_FaceFace", "IntTools_ListIteratorOfListOfBox", "IntTools_ListIteratorOfListOfCurveRangeSample", "IntTools_ListIteratorOfListOfSurfaceRangeSample", "IntTools_ListOfBox", "IntTools_ListOfCurveRangeSample", "IntTools_ListOfSurfaceRangeSample", "IntTools_MapOfCurveSample", "IntTools_MapOfSurfaceSample", "IntTools_MarkedRangeSet", "IntTools_PntOn2Faces", "IntTools_PntOnFace", "IntTools_Range", "IntTools_Root", "IntTools_SequenceOfCommonPrts", "IntTools_SequenceOfCurves", "IntTools_SequenceOfPntOn2Faces", "IntTools_SequenceOfRanges", "IntTools_SequenceOfRoots", "IntTools_ShrunkRange", "IntTools_SurfaceRangeLocalizeData", "IntTools_SurfaceRangeSample", "IntTools_Tools", "IntTools_TopolTool", "IntTools_WLineTool", "SwigPyIterator", "_SwigNonDynamicMeta", "inttools", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.IntWalk", "type": "module", "classes": ["IntWalk_StatusDeflection", "IntWalk_TheFunctionOfTheInt2S", "IntWalk_TheInt2S", "IntWalk_WalkingData", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Interface", "type": "module", "classes": ["Interface_Array1OfFileParameter", "Interface_Array1OfHAsciiString", "Interface_BitMap", "Interface_Category", "Interface_Check", "Interface_CheckIterator", "Interface_CheckStatus", "Interface_CheckTool", "Interface_CopyControl", "Interface_CopyMap", "Interface_CopyTool", "Interface_DataMapOfTransientInteger", "Interface_DataState", "Interface_EntityCluster", "Interface_EntityIterator", "Interface_EntityList", "Interface_FileParameter", "Interface_FileReaderData", "Interface_FileReaderTool", "Interface_FloatWriter", "Interface_GTool", "Interface_GeneralLib", "Interface_GeneralModule", "Interface_GlobalNodeOfGeneralLib", "Interface_GlobalNodeOfReaderLib", "Interface_Graph", "Interface_GraphContent", "Interface_HArray1OfHAsciiString", "Interface_HGraph", "Interface_HSequenceOfCheck", "Interface_IndexedMapOfAsciiString", "Interface_IntList", "Interface_IntVal", "Interface_InterfaceModel", "Interface_LineBuffer", "Interface_MSG", "Interface_NodeOfGeneralLib", "Interface_NodeOfReaderLib", "Interface_ParamList", "Interface_ParamSet", "Interface_ParamType", "Interface_Protocol", "Interface_ReaderLib", "Interface_ReaderModule", "Interface_ReportEntity", "Interface_STAT", "Interface_SequenceOfCheck", "Interface_ShareFlags", "Interface_ShareTool", "Interface_SignLabel", "Interface_SignType", "Interface_Static", "Interface_TypedValue", "Interface_UndefinedContent", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.InterfaceGraphic", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Intf", "type": "module", "classes": ["Intf_Array1OfLin", "Intf_Interference", "Intf_InterferencePolygon2d", "Intf_PIType", "Intf_Polygon2d", "Intf_SectionLine", "Intf_SectionPoint", "Intf_SeqOfSectionLine", "Intf_SeqOfSectionPoint", "Intf_SeqOfTangentZone", "Intf_TangentZone", "Intf_Tool", "SwigPyIterator", "_SwigNonDynamicMeta", "intf", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Intrv", "type": "module", "classes": ["Intrv_Interval", "Intrv_Intervals", "Intrv_Position", "Intrv_SequenceOfInterval", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.LDOM", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.LProp", "type": "module", "classes": ["LProp_AnalyticCurInf", "LProp_CIType", "LProp_CurAndInf", "LProp_SequenceOfCIType", "LProp_Status", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.LProp3d", "type": "module", "classes": ["LProp3d_CLProps", "LProp3d_CurveTool", "LProp3d_SLProps", "LProp3d_SurfaceTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Law", "type": "module", "classes": ["Law_BSpFunc", "Law_BSpline", "Law_BSplineKnotSplitting", "Law_Composite", "Law_Constant", "Law_Function", "Law_Interpol", "Law_Interpolate", "Law_Laws", "Law_Linear", "Law_ListIteratorOfLaws", "Law_S", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "law", "ostream"], "children": []}, {"name": "OCC.Core.LocOpe", "type": "module", "classes": ["LocOpe_BuildShape", "LocOpe_BuildWires", "LocOpe_CSIntersector", "LocOpe_CurveShapeIntersector", "LocOpe_DPrism", "LocOpe_DataMapOfShapePnt", "LocOpe_FindEdges", "LocOpe_FindEdgesInFace", "LocOpe_GeneratedShape", "LocOpe_Generator", "LocOpe_GluedShape", "LocOpe_Gluer", "LocOpe_LinearForm", "LocOpe_Operation", "LocOpe_Pipe", "LocOpe_PntFace", "LocOpe_Prism", "LocOpe_SequenceOfCirc", "LocOpe_SequenceOfLin", "LocOpe_SequenceOfPntFace", "LocOpe_SplitDrafts", "LocOpe_SplitShape", "LocOpe_Spliter", "LocOpe_WiresOnShape", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "locope", "ostream"], "children": []}, {"name": "OCC.Core.LocalAnalysis", "type": "module", "classes": ["LocalAnalysis_CurveContinuity", "LocalAnalysis_StatusErrorType", "LocalAnalysis_SurfaceContinuity", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "localanalysis", "ostream"], "children": []}, {"name": "OCC.Core.MAT", "type": "module", "classes": ["MAT_Arc", "MAT_BasicElt", "MAT_Bisector", "MAT_DataMapOfIntegerArc", "MAT_DataMapOfIntegerBasicElt", "MAT_DataMapOfIntegerBisector", "MAT_DataMapOfIntegerNode", "MAT_Edge", "MAT_Graph", "MAT_ListOfBisector", "MAT_ListOfEdge", "MAT_Node", "MAT_SequenceOfArc", "MAT_SequenceOfBasicElt", "MAT_Side", "MAT_TListNodeOfListOfBisector", "MAT_TListNodeOfListOfEdge", "MAT_Zone", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.MAT2d", "type": "module", "classes": ["MAT2d_BiInt", "MAT2d_Circuit", "MAT2d_Connexion", "MAT2d_DataMapOfBiIntInteger", "MAT2d_DataMapOfBiIntSequenceOfInteger", "MAT2d_DataMapOfIntegerBisec", "MAT2d_DataMapOfIntegerConnexion", "MAT2d_DataMapOfIntegerPnt2d", "MAT2d_DataMapOfIntegerSequenceOfConnexion", "MAT2d_DataMapOfIntegerVec2d", "MAT2d_Mat2d", "MAT2d_MiniPath", "MAT2d_SequenceOfConnexion", "MAT2d_SequenceOfSequenceOfCurve", "MAT2d_SequenceOfSequenceOfGeometry", "MAT2d_Tool2d", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Media", "type": "module", "classes": ["Media_BufferPool", "Media_CodecContext", "Media_FormatContext", "Media_Frame", "Media_IFrameQueue", "Media_Packet", "Media_PlayerContext", "Media_Scaler", "Media_Timer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.MeshDS", "type": "module", "classes": ["MeshDS_DataSource", "SwigPyIterator", "_SwigNonDynamicMeta", "vector_float", "vector_gp_Pnt", "vector_int", "vector_vec", "vector_vector_int", "vector_vector_vec"], "children": []}, {"name": "OCC.Core.MeshVS", "type": "module", "classes": ["MeshVS_Array1OfSequenceOfInteger", "Me<PERSON><PERSON>_<PERSON>uffer", "MeshVS_CommonSensitiveEntity", "MeshVS_DataMapOfColorMapOfInteger", "MeshVS_DataMapOfHArray1OfSequenceOfInteger", "MeshVS_DataMapOfIntegerAsciiString", "MeshVS_DataMapOfIntegerBoolean", "MeshVS_DataMapOfIntegerColor", "MeshVS_DataMapOfIntegerMaterial", "MeshVS_DataMapOfIntegerMeshEntityOwner", "MeshVS_DataMapOfIntegerOwner", "MeshVS_DataMapOfIntegerTwoColors", "MeshVS_DataMapOfIntegerVector", "MeshVS_DataMapOfTwoColorsMapOfInteger", "MeshVS_DataSource", "MeshVS_DataSource3D", "MeshVS_DeformedDataSource", "MeshVS_Drawer", "MeshVS_DrawerAttribute", "MeshVS_DummySensitiveEntity", "MeshVS_ElementalColorPrsBuilder", "MeshVS_EntityType", "MeshVS_HArray1OfSequenceOfInteger", "MeshVS_MapOfTwoNodes", "MeshVS_Mesh", "MeshVS_MeshEntityOwner", "MeshVS_MeshOwner", "MeshVS_MeshPrsBuilder", "MeshVS_MeshSelectionMethod", "MeshVS_NodalColorPrsBuilder", "MeshVS_PolyhedronVerts", "MeshVS_PolyhedronVertsIter", "MeshVS_PrsBuilder", "MeshVS_SelectionModeFlags", "MeshVS_SensitiveFace", "Mesh<PERSON>_SensitiveMesh", "MeshVS_SensitivePolyhedron", "MeshVS_SensitiveQuad", "MeshVS_SensitiveSegment", "MeshVS_SequenceOfPrsBuilder", "MeshVS_SymmetricPairHasher", "MeshVS_TextPrsBuilder", "MeshVS_Tool", "MeshVS_TwoColors", "MeshVS_TwoNodes", "MeshVS_VectorPrsBuilder", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Message", "type": "module", "classes": ["Message_Alert", "Message_AlertExtended", "Message_Algorithm", "Message_Attribute", "Message_AttributeMeter", "Message_AttributeObject", "Message_AttributeStream", "Message_CompositeAlerts", "Message_ConsoleColor", "Message_ExecStatus", "Message_Gravity", "Message_Level", "Message_ListIteratorOfListOfMsg", "Message_ListOfAlert", "Message_ListOfMsg", "Message_Messenger", "Message_MetricType", "Message_Msg", "Message_MsgFile", "Message_Printer", "Message_PrinterOStream", "Message_PrinterSystemLog", "Message_PrinterToReport", "Message_ProgressIndicator", "Message_ProgressRange", "Message_ProgressSentry", "Message_Report", "Message_SequenceOfPrinters", "Message_Status", "Message_StatusType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "message", "ostream"], "children": []}, {"name": "OCC.Core.MoniTool", "type": "module", "classes": ["MoniTool_AttrList", "MoniTool_CaseData", "MoniTool_DataInfo", "MoniTool_DataMapOfShapeTransient", "MoniTool_DataMapOfTimer", "MoniTool_Element", "MoniTool_HSequenceOfElement", "MoniTool_IndexedDataMapOfShapeTransient", "MoniTool_IntVal", "MoniTool_RealVal", "MoniTool_SequenceOfElement", "MoniTool_SignShape", "MoniTool_SignText", "MoniTool_Stat", "MoniTool_Timer", "MoniTool_TimerSentry", "MoniTool_TransientElem", "MoniTool_TypedValue", "MoniTool_ValueType", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.NCollection", "type": "module", "classes": ["NCollection_CellFilter_Action", "NCollection_String", "NCollection_Utf16Iter", "NCollection_Utf16String", "NCollection_Utf32Iter", "NCollection_Utf32String", "NCollection_Utf8Iter", "NCollection_Utf8String", "NCollection_UtfWideIter", "NCollection_UtfWideString", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.NLPlate", "type": "module", "classes": ["NLPlate_HGPPConstraint", "NLPlate_HPG0Constraint", "NLPlate_HPG0G1Constraint", "NLPlate_HPG0G2Constraint", "NLPlate_HPG0G3Constraint", "NLPlate_HPG1Constraint", "NLPlate_HPG2Constraint", "NLPlate_HPG3Constraint", "NLPlate_ListIteratorOfStackOfPlate", "NLPlate_NLPlate", "NLPlate_SequenceOfHGPPConstraint", "NLPlate_StackOfPlate", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.OSD", "type": "module", "classes": ["OSD_FromWhere", "OSD_KindFile", "OSD_LoadMode", "OSD_LockType", "OSD_OEMType", "OSD_OpenMode", "OSD_SignalMode", "OSD_SingleProtection", "OSD_SysType", "OSD_WhoAmI", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.PCDM", "type": "module", "classes": ["PCDM_BaseDriverPointer", "PCDM_ReadWriter", "PCDM_ReadWriter_1", "PCDM_Reader", "PCDM_ReaderFilter", "PCDM_ReaderStatus", "PCDM_Reference", "PCDM_ReferenceIterator", "PCDM_RetrievalDriver", "PCDM_SequenceOfDocument", "PCDM_SequenceOfReference", "PCDM_StorageDriver", "PCDM_StoreStatus", "PCDM_TypeOfFileDriver", "PCDM_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "pcdm"], "children": []}, {"name": "OCC.Core.PLib", "type": "module", "classes": ["PLib_Base", "PLib_DoubleJacobiPolynomial", "<PERSON><PERSON><PERSON>_<PERSON>", "PLib_JacobiPolynomial", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "plib"], "children": []}, {"name": "OCC.Core.Plate", "type": "module", "classes": ["Plate_Array1OfPinpointConstraint", "Plate_D1", "Plate_D2", "Plate_D3", "Plate_FreeGtoCConstraint", "Plate_GlobalTranslationConstraint", "Plate_GtoCConstraint", "Plate_HArray1OfPinpointConstraint", "Plate_LineConstraint", "Plate_LinearScalarConstraint", "Plate_LinearXYZConstraint", "Plate_PinpointConstraint", "Plate_PlaneConstraint", "Plate_Plate", "Plate_SampledCurveConstraint", "Plate_SequenceOfLinearScalarConstraint", "Plate_SequenceOfLinearXYZConstraint", "Plate_SequenceOfPinpointConstraint", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Plugin", "type": "module", "classes": ["Plugin_MapOfFunctions", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Poly", "type": "module", "classes": ["Poly_Array1OfTriangle", "Poly_ArrayOfNodes", "Poly_ArrayOfUVNodes", "Poly_CoherentLink", "Poly_CoherentNode", "Poly_CoherentTriangle", "Poly_CoherentTriangulation", "Poly_Connect", "Poly_HArray1OfTriangle", "Poly_ListOfTriangulation", "Poly_MergeNodesTool", "Poly_Polygon2D", "Poly_Polygon3D", "Poly_PolygonOnTriangulation", "Poly_Triangle", "Poly_Triangulation", "Poly_TriangulationParameters", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "poly"], "children": []}, {"name": "OCC.Core.Precision", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "precision"], "children": []}, {"name": "OCC.Core.ProjLib", "type": "module", "classes": ["ProjLib_CompProjectedCurve", "ProjLib_ComputeApprox", "ProjLib_ComputeApproxOnPolarSurface", "ProjLib_Cone", "ProjLib_Cylinder", "ProjLib_HCompProjectedCurve", "ProjLib_HProjectedCurve", "ProjLib_HSequenceOfHSequenceOfPnt", "ProjLib_Plane", "ProjLib_PrjFunc", "ProjLib_PrjResolve", "ProjLib_ProjectOnPlane", "ProjLib_ProjectOnSurface", "ProjLib_ProjectedCurve", "ProjLib_Projector", "ProjLib_SequenceOfHSequenceOfPnt", "ProjLib_Sphere", "ProjLib_Torus", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "<PERSON>j<PERSON><PERSON>"], "children": []}, {"name": "OCC.Core.Prs3d", "type": "module", "classes": ["Prs3d_Arrow", "Prs3d_ArrowAspect", "Prs3d_BasicAspect", "Prs3d_BndBox", "Prs3d_DatumAspect", "Prs3d_DatumAttribute", "Prs3d_DatumAxes", "Prs3d_DatumMode", "Prs3d_DatumParts", "Prs3d_DimensionArrowOrientation", "Prs3d_DimensionAspect", "Prs3d_DimensionTextHorizontalPosition", "Prs3d_DimensionTextVerticalPosition", "Prs3d_DimensionUnits", "Prs3d_Drawer", "Prs3d_IsoAspect", "Prs3d_LineAspect", "Prs3d_NListOfSequenceOfPnt", "Prs3d_PlaneAspect", "Prs3d_PointAspect", "Prs3d_PresentationShadow", "Prs3d_Root", "Prs3d_ShadingAspect", "Prs3d_Text", "Prs3d_TextAspect", "Prs3d_ToolCylinder", "Prs3d_ToolDisk", "Prs3d_ToolQuadric", "Prs3d_ToolSector", "Prs3d_ToolSphere", "Prs3d_ToolTorus", "Prs3d_TypeOfHLR", "Prs3d_TypeOfHighlight", "Prs3d_TypeOfLinePicking", "Prs3d_VertexDrawMode", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "prs3d"], "children": []}, {"name": "OCC.Core.PrsDim", "type": "module", "classes": ["PrsDim_AngleDimension", "PrsDim_Chamf2dDimension", "PrsDim_Chamf3dDimension", "PrsDim_ConcentricRelation", "PrsDim_DiameterDimension", "PrsDim_Dimension", "PrsDim_DimensionOwner", "PrsDim_DimensionSelectionMode", "PrsDim_DisplaySpecialSymbol", "PrsDim_EllipseRadiusDimension", "PrsDim_EqualDistanceRelation", "PrsDim_EqualRadiusRelation", "PrsDim_FixRelation", "PrsDim_IdenticRelation", "PrsDim_KindOfDimension", "PrsDim_KindOfRelation", "PrsDim_KindOfSurface", "PrsDim_LengthDimension", "PrsDim_MaxRadiusDimension", "PrsDim_MidPointRelation", "PrsDim_MinRadiusDimension", "PrsDim_OffsetDimension", "PrsDim_ParallelRelation", "PrsDim_PerpendicularRelation", "PrsDim_RadiusDimension", "PrsDim_Relation", "PrsDim_SymmetricRelation", "PrsDim_TangentRelation", "PrsDim_TypeOfAngle", "PrsDim_TypeOfAngleArrowVisibility", "PrsDim_TypeOfDist", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "prsdim"], "children": []}, {"name": "OCC.Core.PrsMgr", "type": "module", "classes": ["PrsMgr_DisplayStatus", "PrsMgr_ListOfPresentableObjects", "PrsMgr_ListOfPresentableObjectsIter", "PrsMgr_ListOfPresentations", "PrsMgr_PresentableObject", "PrsMgr_Presentation", "PrsMgr_Presentation3d", "PrsMgr_PresentationManager", "PrsMgr_PresentationManager3d", "PrsMgr_Presentations", "PrsMgr_TypeOfPresentation3d", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Quantity", "type": "module", "classes": ["Quantity_Array1OfColor", "Quantity_Color", "Quantity_ColorRGBA", "Quantity_Date", "Quantity_HArray1OfColor", "Quantity_NameOfColor", "Quantity_Period", "Quantity_TypeOfColor", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWGltf", "type": "module", "classes": ["RWGltf_CafReader", "RWGltf_CafWriter", "RWGltf_DracoParameters", "RWGltf_GltfAccessor", "RWGltf_GltfAccessorCompType", "RWGltf_GltfAccessorLayout", "RWGltf_GltfAlphaMode", "RWGltf_GltfArrayType", "RWGltf_GltfBufferView", "RWGltf_GltfBufferViewTarget", "RWGltf_GltfFace", "RWGltf_GltfLatePrimitiveArray", "RWGltf_GltfMaterialMap", "RWGltf_GltfOStreamWriter", "RWGltf_GltfPrimArrayData", "RWGltf_GltfPrimitiveMode", "RWGltf_GltfRootElement", "RWGltf_GltfSceneNodeMap", "RWGltf_MaterialCommon", "RWGltf_MaterialMetallicRoughness", "RWGltf_TriangulationReader", "RWGltf_WriterTrsfFormat", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWHeaderSection", "type": "module", "classes": ["RWHeaderSection_GeneralModule", "RWHeaderSection_RWFileDescription", "RWHeaderSection_RWFileName", "RWHeaderSection_RWFileSchema", "RWHeaderSection_ReadWriteModule", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "rwheadersection"], "children": []}, {"name": "OCC.Core.RWMesh", "type": "module", "classes": ["RWMesh_CafReader", "RWMesh_CafReaderStatusEx", "RWMesh_CoordinateSystem", "RWMesh_CoordinateSystemConverter", "RWMesh_EdgeIterator", "RWMesh_FaceIterator", "RWMesh_MaterialMap", "RWMesh_NameFormat", "RWMesh_NodeAttributeMap", "RWMesh_NodeAttributes", "RWMesh_ShapeIterator", "RWMesh_TriangulationReader", "RWMesh_TriangulationSource", "RWMesh_VertexIterator", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "rwmesh"], "children": []}, {"name": "OCC.Core.RWObj", "type": "module", "classes": ["RWObj_CafReader", "RWObj_CafWriter", "RWObj_IShapeReceiver", "RWObj_Material", "RWObj_ObjMaterialMap", "RWObj_ObjWriterContext", "RWObj_Reader", "RWObj_SubMesh", "RWObj_SubMeshReason", "RWObj_TriangulationReader", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "rwobj"], "children": []}, {"name": "OCC.Core.RWPly", "type": "module", "classes": ["RWPly_CafWriter", "RWPly_PlyWriterContext", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepAP203", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepAP214", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepAP242", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepBasic", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepDimTol", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepElement", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepFEA", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepGeom", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepKinematics", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepRepr", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepShape", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStepVisual", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.RWStl", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "rwstl"], "children": []}, {"name": "OCC.Core.Resource", "type": "module", "classes": ["Resource_DataMapOfAsciiStringAsciiString", "Resource_DataMapOfAsciiStringExtendedString", "Resource_FormatType", "Resource_LexicalCompare", "Resource_Manager", "Resource_Unicode", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.STEPCAFControl", "type": "module", "classes": ["STEPCAFControl_ActorWrite", "STEPCAFControl_Controller", "STEPCAFControl_DataMapOfLabelExternFile", "STEPCAFControl_DataMapOfLabelShape", "STEPCAFControl_DataMapOfPDExternFile", "STEPCAFControl_DataMapOfSDRExternFile", "STEPCAFControl_DataMapOfShapePD", "STEPCAFControl_DataMapOfShapeSDR", "STEPCAFControl_ExternFile", "STEPCAFControl_GDTProperty", "STEPCAFControl_Reader", "STEPCAFControl_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.STEPConstruct", "type": "module", "classes": ["STEPConstruct_AP203Context", "STEPConstruct_Assembly", "STEPConstruct_ContextTool", "STEPConstruct_DataMapOfAsciiStringTransient", "STEPConstruct_DataMapOfPointTransient", "STEPConstruct_ExternRefs", "STEPConstruct_Part", "STEPConstruct_Styles", "STEPConstruct_Tool", "STEPConstruct_UnitContext", "STEPConstruct_ValidationProps", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stepconstruct"], "children": []}, {"name": "OCC.Core.STEPControl", "type": "module", "classes": ["STEPControl_ActorRead", "STEPControl_ActorWrite", "STEPControl_Controller", "STEPControl_Reader", "STEPControl_StepModelType", "STEPControl_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.STEPEdit", "type": "module", "classes": ["STEPEdit_EditContext", "STEPEdit_EditSDR", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stepedit"], "children": []}, {"name": "OCC.Core.STEPSelections", "type": "module", "classes": ["STEPSelections_AssemblyComponent", "STEPSelections_AssemblyExplorer", "STEPSelections_AssemblyLink", "STEPSelections_Counter", "STEPSelections_HSequenceOfAssemblyLink", "STEPSelections_SelectAssembly", "STEPSelections_SelectDerived", "STEPSelections_SelectFaces", "STEPSelections_SelectForTransfer", "STEPSelections_SelectGSCurves", "STEPSelections_SelectInstances", "STEPSelections_SequenceOfAssemblyComponent", "STEPSelections_SequenceOfAssemblyLink", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Select3D", "type": "module", "classes": ["Select3D_BVHIndexBuffer", "Select3D_EntitySequence", "Select3D_EntitySequenceIter", "Select3D_IndexedMapOfEntity", "Select3D_InteriorSensitivePointSet", "Select3D_Pnt", "Select3D_PointData", "Select3D_SensitiveBox", "Select3D_SensitiveCurve", "Select3D_SensitiveCylinder", "Select3D_SensitiveFace", "Select3D_SensitiveGroup", "Select3D_SensitivePoint", "Select3D_SensitivePoly", "Select3D_SensitivePrimitiveArray", "Select3D_SensitiveSegment", "Select3D_SensitiveSphere", "Select3D_SensitiveTriangle", "Select3D_SensitiveWire", "Select3D_TypeOfSensitivity", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.SelectBasics", "type": "module", "classes": ["SelectBasics_PickResult", "SelectBasics_SelectingVolumeManager", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "selectbasics"], "children": []}, {"name": "OCC.Core.SelectMgr", "type": "module", "classes": ["SelectBasics_EntityOwner", "SelectMgr_AndFilter", "SelectMgr_AndOrFilter", "SelectMgr_AxisIntersector", "SelectMgr_BVHThreadPool", "SelectMgr_BaseIntersector", "SelectMgr_CompositionFilter", "SelectMgr_EntityOwner", "SelectMgr_Filter", "SelectMgr_FilterType", "SelectMgr_FrustumBuilder", "SelectMgr_FrustumCache", "SelectMgr_IndexedDataMapOfOwnerCriterion", "SelectMgr_IndexedMapOfHSensitive", "SelectMgr_ListIteratorOfListOfFilter", "SelectMgr_ListOfFilter", "SelectMgr_MapOfOwners", "SelectMgr_OrFilter", "SelectMgr_PickingStrategy", "SelectMgr_SelectableObject", "SelectMgr_SelectableObjectSet", "SelectMgr_SelectingVolumeManager", "SelectMgr_Selection", "SelectMgr_SelectionImageFiller", "SelectMgr_SelectionManager", "SelectMgr_SelectionType", "SelectMgr_SensitiveEntity", "SelectMgr_SensitiveEntitySet", "SelectMgr_SequenceOfOwner", "SelectMgr_SequenceOfSelection", "SelectMgr_SortCriterion", "SelectMgr_StateOfSelection", "SelectMgr_ToleranceMap", "SelectMgr_TypeOfBVHUpdate", "SelectMgr_TypeOfDepthTolerance", "SelectMgr_TypeOfUpdate", "SelectMgr_ViewClipRange", "SelectMgr_ViewerSelector", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "selectmgr"], "children": []}, {"name": "OCC.Core.ShapeAlgo", "type": "module", "classes": ["ShapeAlgo_ToolContainer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapealgo"], "children": []}, {"name": "OCC.Core.ShapeAnalysis", "type": "module", "classes": ["ShapeAnalysis_BoxBndTree", "ShapeAnalysis_CanonicalRecognition", "ShapeAnalysis_CheckSmallFace", "ShapeAnalysis_Curve", "ShapeAnalysis_DataMapOfShapeListOfReal", "ShapeAnalysis_Edge", "ShapeAnalysis_FreeBoundData", "ShapeAnalysis_FreeBounds", "ShapeAnalysis_FreeBoundsProperties", "ShapeAnalysis_Geom", "ShapeAnalysis_HSequenceOfFreeBounds", "ShapeAnalysis_SequenceOfFreeBounds", "ShapeAnalysis_ShapeContents", "ShapeAnalysis_ShapeTolerance", "ShapeAnalysis_Shell", "ShapeAnalysis_Surface", "ShapeAnalysis_TransferParameters", "ShapeAnalysis_TransferParametersProj", "ShapeAnalysis_Wire", "ShapeAnalysis_WireOrder", "ShapeAnalysis_WireVertex", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapeanalysis"], "children": []}, {"name": "OCC.Core.ShapeBuild", "type": "module", "classes": ["ShapeBuild_Edge", "ShapeBuild_ReShape", "ShapeBuild_Vertex", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapebuild"], "children": []}, {"name": "OCC.Core.ShapeConstruct", "type": "module", "classes": ["ShapeConstruct_Curve", "ShapeConstruct_MakeTriangulation", "ShapeConstruct_ProjectCurveOnSurface", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapeconstruct"], "children": []}, {"name": "OCC.Core.ShapeCustom", "type": "module", "classes": ["ShapeCustom_ConvertToBSpline", "ShapeCustom_Curve", "ShapeCustom_Curve2d", "ShapeCustom_DirectModification", "ShapeCustom_Modification", "ShapeCustom_RestrictionParameters", "ShapeCustom_Surface", "ShapeCustom_TrsfModification", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "<PERSON><PERSON><PERSON>"], "children": []}, {"name": "OCC.Core.ShapeExtend", "type": "module", "classes": ["ShapeExtend_BasicMsgRegistrator", "ShapeExtend_ComplexCurve", "ShapeExtend_CompositeSurface", "ShapeExtend_DataMapOfShapeListOfMsg", "ShapeExtend_DataMapOfTransientListOfMsg", "ShapeExtend_Explorer", "ShapeExtend_MsgRegistrator", "ShapeExtend_Parametrisation", "ShapeExtend_Status", "ShapeExtend_WireData", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapeextend"], "children": []}, {"name": "OCC.Core.ShapeFix", "type": "module", "classes": ["ShapeFix_ComposeShell", "ShapeFix_DataMapOfShapeBox2d", "ShapeFix_Edge", "ShapeFix_EdgeConnect", "ShapeFix_EdgeProjAux", "ShapeFix_Face", "ShapeFix_FaceConnect", "ShapeFix_FixSmallFace", "ShapeFix_FixSmallSolid", "ShapeFix_FreeBounds", "ShapeFix_IntersectionTool", "ShapeFix_Root", "ShapeFix_SequenceOfWireSegment", "ShapeFix_Shape", "ShapeFix_ShapeTolerance", "ShapeFix_Shell", "ShapeFix_Solid", "ShapeFix_SplitCommonVertex", "ShapeFix_SplitTool", "ShapeFix_Wire", "ShapeFix_WireVertex", "ShapeFix_Wireframe", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapefix"], "children": []}, {"name": "OCC.Core.ShapeProcess", "type": "module", "classes": ["ShapeProcess_Context", "ShapeProcess_OperLibrary", "ShapeProcess_Operator", "ShapeProcess_ShapeContext", "ShapeProcess_UOperator", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapeprocess"], "children": []}, {"name": "OCC.Core.ShapeProcessAPI", "type": "module", "classes": ["ShapeProcessAPI_ApplySequence", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.ShapeUpgrade", "type": "module", "classes": ["ShapeUpgrade_ClosedEdgeDivide", "ShapeUpgrade_ClosedFaceDivide", "ShapeUpgrade_ConvertCurve2dToBezier", "ShapeUpgrade_ConvertCurve3dToBezier", "ShapeUpgrade_ConvertSurfaceToBezierBasis", "ShapeUpgrade_EdgeDivide", "ShapeUpgrade_FaceDivide", "ShapeUpgrade_FaceDivideArea", "ShapeUpgrade_FixSmallBezierCurves", "ShapeUpgrade_FixSmallCurves", "ShapeUpgrade_RemoveInternalWires", "ShapeUpgrade_RemoveLocations", "ShapeUpgrade_ShapeConvertToBezier", "ShapeUpgrade_ShapeDivide", "ShapeUpgrade_ShapeDivideAngle", "ShapeUpgrade_ShapeDivideArea", "ShapeUpgrade_ShapeDivideClosed", "ShapeUpgrade_ShapeDivideClosedEdges", "ShapeUpgrade_ShapeDivideContinuity", "ShapeUpgrade_ShellSewing", "ShapeUpgrade_SplitCurve", "ShapeUpgrade_SplitCurve2d", "ShapeUpgrade_SplitCurve2dContinuity", "ShapeUpgrade_SplitCurve3d", "ShapeUpgrade_SplitCurve3dContinuity", "ShapeUpgrade_SplitSurface", "ShapeUpgrade_SplitSurfaceAngle", "ShapeUpgrade_SplitSurfaceArea", "ShapeUpgrade_SplitSurfaceContinuity", "ShapeUpgrade_Tool", "ShapeUpgrade_UnifySameDomain", "ShapeUpgrade_WireDivide", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "shapeupgrade"], "children": []}, {"name": "OCC.Core.Standard", "type": "module", "classes": ["Standard_ArrayStreamBuffer", "Standard_CStringHasher", "Standard_Condition", "Standard_ErrorHandler", "Standard_Failure", "Standard_GUID", "Standard_HandlerStatus", "Standard_MMgrOpt", "Standard_MMgrRoot", "Standard_OutOfMemory", "Standard_Persistent", "Standard_Transient", "Standard_Type", "Standard_UUID", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "standard"], "children": []}, {"name": "OCC.Core.StdFail", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StdPrs", "type": "module", "classes": ["StdPrs_BRepFont", "StdPrs_BRepTextBuilder", "StdPrs_Curve", "StdPrs_HLRPolyShape", "StdPrs_HLRShape", "StdPrs_HLRShapeI", "StdPrs_HLRToolShape", "StdPrs_Isolines", "StdPrs_Plane", "StdPrs_PoleCurve", "StdPrs_ShadedShape", "StdPrs_ShadedSurface", "StdPrs_ShapeTool", "StdPrs_ToolPoint", "StdPrs_ToolRFace", "StdPrs_ToolTriangulatedShape", "StdPrs_ToolVertex", "StdPrs_Volume", "StdPrs_WFDeflectionRestrictedFace", "StdPrs_WFDeflectionSurface", "StdPrs_WFPoleSurface", "StdPrs_WFRestrictedFace", "StdPrs_WFShape", "StdPrs_WFSurface", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StdSelect", "type": "module", "classes": ["StdSelect_BRepOwner", "StdSelect_BRepSelectionTool", "StdSelect_EdgeFilter", "StdSelect_FaceFilter", "StdSelect_Shape", "StdSelect_ShapeTypeFilter", "StdSelect_TypeOfEdge", "StdSelect_TypeOfFace", "StdSelect_TypeOfSelectionImage", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stdselect"], "children": []}, {"name": "OCC.Core.StepAP203", "type": "module", "classes": ["StepAP203_ApprovedItem", "StepAP203_Array1OfApprovedItem", "StepAP203_Array1OfCertifiedItem", "StepAP203_Array1OfChangeRequestItem", "StepAP203_Array1OfClassifiedItem", "StepAP203_Array1OfContractedItem", "StepAP203_Array1OfDateTimeItem", "StepAP203_Array1OfPersonOrganizationItem", "StepAP203_Array1OfSpecifiedItem", "StepAP203_Array1OfStartRequestItem", "StepAP203_Array1OfWorkItem", "StepAP203_CcDesignApproval", "StepAP203_CcDesignCertification", "StepAP203_CcDesignContract", "StepAP203_CcDesignDateAndTimeAssignment", "StepAP203_CcDesignPersonAndOrganizationAssignment", "StepAP203_CcDesignSecurityClassification", "StepAP203_CcDesignSpecificationReference", "StepAP203_CertifiedItem", "StepAP203_Change", "StepAP203_ChangeRequest", "StepAP203_ChangeRequestItem", "StepAP203_ClassifiedItem", "StepAP203_ContractedItem", "StepAP203_DateTimeItem", "StepAP203_HArray1OfApprovedItem", "StepAP203_HArray1OfCertifiedItem", "StepAP203_HArray1OfChangeRequestItem", "StepAP203_HArray1OfClassifiedItem", "StepAP203_HArray1OfContractedItem", "StepAP203_HArray1OfDateTimeItem", "StepAP203_HArray1OfPersonOrganizationItem", "StepAP203_HArray1OfSpecifiedItem", "StepAP203_HArray1OfStartRequestItem", "StepAP203_HArray1OfWorkItem", "StepAP203_PersonOrganizationItem", "StepAP203_SpecifiedItem", "StepAP203_StartRequest", "StepAP203_StartRequestItem", "StepAP203_StartWork", "StepAP203_WorkItem", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepAP209", "type": "module", "classes": ["StepAP209_Construct", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepAP214", "type": "module", "classes": ["StepAP214_AppliedApprovalAssignment", "StepAP214_AppliedDateAndTimeAssignment", "StepAP214_AppliedDateAssignment", "StepAP214_AppliedDocumentReference", "StepAP214_AppliedExternalIdentificationAssignment", "StepAP214_AppliedGroupAssignment", "StepAP214_AppliedOrganizationAssignment", "StepAP214_AppliedPersonAndOrganizationAssignment", "StepAP214_AppliedPresentedItem", "StepAP214_AppliedSecurityClassificationAssignment", "StepAP214_ApprovalItem", "StepAP214_Array1OfApprovalItem", "StepAP214_Array1OfAutoDesignDateAndPersonItem", "StepAP214_Array1OfAutoDesignDateAndTimeItem", "StepAP214_Array1OfAutoDesignDatedItem", "StepAP214_Array1OfAutoDesignGeneralOrgItem", "StepAP214_Array1OfAutoDesignGroupedItem", "StepAP214_Array1OfAutoDesignPresentedItemSelect", "StepAP214_Array1OfAutoDesignReferencingItem", "StepAP214_Array1OfDateAndTimeItem", "StepAP214_Array1OfDateItem", "StepAP214_Array1OfDocumentReferenceItem", "StepAP214_Array1OfExternalIdentificationItem", "StepAP214_Array1OfGroupItem", "StepAP214_Array1OfOrganizationItem", "StepAP214_Array1OfPersonAndOrganizationItem", "StepAP214_Array1OfPresentedItemSelect", "StepAP214_Array1OfSecurityClassificationItem", "StepAP214_AutoDesignActualDateAndTimeAssignment", "StepAP214_AutoDesignActualDateAssignment", "StepAP214_AutoDesignApprovalAssignment", "StepAP214_AutoDesignDateAndPersonAssignment", "StepAP214_AutoDesignDateAndPersonItem", "StepAP214_AutoDesignDateAndTimeItem", "StepAP214_AutoDesignDatedItem", "StepAP214_AutoDesignDocumentReference", "StepAP214_AutoDesignGeneralOrgItem", "StepAP214_AutoDesignGroupAssignment", "StepAP214_AutoDesignGroupedItem", "StepAP214_AutoDesignNominalDateAndTimeAssignment", "StepAP214_AutoDesignNominalDateAssignment", "StepAP214_AutoDesignOrganizationAssignment", "StepAP214_AutoDesignOrganizationItem", "StepAP214_AutoDesignPersonAndOrganizationAssignment", "StepAP214_AutoDesignPresentedItem", "StepAP214_AutoDesignPresentedItemSelect", "StepAP214_AutoDesignReferencingItem", "StepAP214_AutoDesignSecurityClassificationAssignment", "StepAP214_Class", "StepAP214_DateAndTimeItem", "StepAP214_DateItem", "StepAP214_DocumentReferenceItem", "StepAP214_ExternalIdentificationItem", "StepAP214_ExternallyDefinedClass", "StepAP214_ExternallyDefinedGeneralProperty", "StepAP214_GroupItem", "StepAP214_HArray1OfApprovalItem", "StepAP214_HArray1OfAutoDesignDateAndPersonItem", "StepAP214_HArray1OfAutoDesignDateAndTimeItem", "StepAP214_HArray1OfAutoDesignDatedItem", "StepAP214_HArray1OfAutoDesignGeneralOrgItem", "StepAP214_HArray1OfAutoDesignGroupedItem", "StepAP214_HArray1OfAutoDesignPresentedItemSelect", "StepAP214_HArray1OfAutoDesignReferencingItem", "StepAP214_HArray1OfDateAndTimeItem", "StepAP214_HArray1OfDateItem", "StepAP214_HArray1OfDocumentReferenceItem", "StepAP214_HArray1OfExternalIdentificationItem", "StepAP214_HArray1OfGroupItem", "StepAP214_HArray1OfOrganizationItem", "StepAP214_HArray1OfPersonAndOrganizationItem", "StepAP214_HArray1OfPresentedItemSelect", "StepAP214_HArray1OfSecurityClassificationItem", "StepAP214_OrganizationItem", "StepAP214_PersonAndOrganizationItem", "StepAP214_PresentedItemSelect", "StepAP214_Protocol", "StepAP214_RepItemGroup", "StepAP214_SecurityClassificationItem", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stepap214"], "children": []}, {"name": "OCC.Core.StepAP242", "type": "module", "classes": ["StepAP242_DraughtingModelItemAssociation", "StepAP242_GeometricItemSpecificUsage", "StepAP242_IdAttribute", "StepAP242_IdAttributeSelect", "StepAP242_ItemIdentifiedRepresentationUsage", "StepAP242_ItemIdentifiedRepresentationUsageDefinition", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepBasic", "type": "module", "classes": ["StepBasic_Action", "StepBasic_ActionAssignment", "StepBasic_ActionMethod", "StepBasic_ActionRequestAssignment", "StepBasic_ActionRequestSolution", "StepBasic_Address", "StepBasic_AheadOrBehind", "StepBasic_ApplicationContext", "StepBasic_ApplicationContextElement", "StepBasic_ApplicationProtocolDefinition", "StepBasic_Approval", "StepBasic_ApprovalAssignment", "StepBasic_ApprovalDateTime", "StepBasic_ApprovalPersonOrganization", "StepBasic_ApprovalRelationship", "StepBasic_ApprovalRole", "StepBasic_ApprovalStatus", "StepBasic_AreaUnit", "StepBasic_Array1OfApproval", "StepBasic_Array1OfDerivedUnitElement", "StepBasic_Array1OfDocument", "StepBasic_Array1OfNamedUnit", "StepBasic_Array1OfOrganization", "StepBasic_Array1OfPerson", "StepBasic_Array1OfProduct", "StepBasic_Array1OfProductContext", "StepBasic_Array1OfProductDefinition", "StepBasic_Array1OfUncertaintyMeasureWithUnit", "StepBasic_CalendarDate", "StepBasic_Certification", "StepBasic_CertificationAssignment", "StepBasic_CertificationType", "StepBasic_CharacterizedObject", "StepBasic_Contract", "StepBasic_ContractAssignment", "StepBasic_ContractType", "StepBasic_ConversionBasedUnit", "StepBasic_ConversionBasedUnitAndAreaUnit", "StepBasic_ConversionBasedUnitAndLengthUnit", "StepBasic_ConversionBasedUnitAndMassUnit", "StepBasic_ConversionBasedUnitAndPlaneAngleUnit", "StepBasic_ConversionBasedUnitAndRatioUnit", "StepBasic_ConversionBasedUnitAndSolidAngleUnit", "StepBasic_ConversionBasedUnitAndTimeUnit", "StepBasic_ConversionBasedUnitAndVolumeUnit", "StepBasic_CoordinatedUniversalTimeOffset", "StepBasic_Date", "StepBasic_DateAndTime", "StepBasic_DateAndTimeAssignment", "StepBasic_DateAssignment", "StepBasic_DateRole", "StepBasic_DateTimeRole", "StepBasic_DateTimeSelect", "StepBasic_DerivedUnit", "StepBasic_DerivedUnitElement", "StepBasic_DesignContext", "StepBasic_DigitalDocument", "StepBasic_DimensionalExponents", "StepBasic_Document", "StepBasic_DocumentFile", "StepBasic_DocumentProductAssociation", "StepBasic_DocumentProductEquivalence", "StepBasic_DocumentReference", "StepBasic_DocumentRelationship", "StepBasic_DocumentRepresentationType", "StepBasic_DocumentType", "StepBasic_DocumentUsageConstraint", "StepBasic_Effectivity", "StepBasic_EffectivityAssignment", "StepBasic_EulerAngles", "StepBasic_ExternalIdentificationAssignment", "StepBasic_ExternalSource", "StepBasic_ExternallyDefinedItem", "StepBasic_GeneralProperty", "StepBasic_GeneralPropertyRelationship", "StepBasic_Group", "StepBasic_GroupAssignment", "StepBasic_GroupRelationship", "StepBasic_HArray1OfApproval", "StepBasic_HArray1OfDerivedUnitElement", "StepBasic_HArray1OfDocument", "StepBasic_HArray1OfNamedUnit", "StepBasic_HArray1OfOrganization", "StepBasic_HArray1OfPerson", "StepBasic_HArray1OfProduct", "StepBasic_HArray1OfProductContext", "StepBasic_HArray1OfProductDefinition", "StepBasic_HArray1OfUncertaintyMeasureWithUnit", "StepBasic_IdentificationAssignment", "StepBasic_IdentificationRole", "StepBasic_LengthMeasureWithUnit", "StepBasic_LengthUnit", "StepBasic_LocalTime", "StepBasic_MassMeasureWithUnit", "StepBasic_MassUnit", "StepBasic_MeasureValueMember", "StepBasic_MeasureWithUnit", "StepBasic_MechanicalContext", "StepBasic_NameAssignment", "StepBasic_NamedUnit", "StepBasic_ObjectRole", "StepBasic_OrdinalDate", "StepBasic_Organization", "StepBasic_OrganizationAssignment", "StepBasic_OrganizationRole", "StepBasic_OrganizationalAddress", "StepBasic_Person", "StepBasic_PersonAndOrganization", "StepBasic_PersonAndOrganizationAssignment", "StepBasic_PersonAndOrganizationRole", "StepBasic_PersonOrganizationSelect", "StepBasic_PersonalAddress", "StepBasic_PhysicallyModeledProductDefinition", "StepBasic_PlaneAngleMeasureWithUnit", "StepBasic_PlaneAngleUnit", "StepBasic_Product", "StepBasic_ProductCategory", "StepBasic_ProductCategoryRelationship", "StepBasic_ProductConceptContext", "StepBasic_ProductContext", "StepBasic_ProductDefinition", "StepBasic_ProductDefinitionContext", "StepBasic_ProductDefinitionEffectivity", "StepBasic_ProductDefinitionFormation", "StepBasic_ProductDefinitionFormationRelationship", "StepBasic_ProductDefinitionFormationWithSpecifiedSource", "StepBasic_ProductDefinitionOrReference", "StepBasic_ProductDefinitionReference", "StepBasic_ProductDefinitionReferenceWithLocalRepresentation", "StepBasic_ProductDefinitionRelationship", "StepBasic_ProductDefinitionWithAssociatedDocuments", "StepBasic_ProductOrFormationOrDefinition", "StepBasic_ProductRelatedProductCategory", "StepBasic_ProductType", "StepBasic_RatioMeasureWithUnit", "StepBasic_RatioUnit", "StepBasic_RoleAssociation", "StepBasic_RoleSelect", "StepBasic_SecurityClassification", "StepBasic_SecurityClassificationAssignment", "StepBasic_SecurityClassificationLevel", "StepBasic_SiPrefix", "StepBasic_SiUnit", "StepBasic_SiUnitAndAreaUnit", "StepBasic_SiUnitAndLengthUnit", "StepBasic_SiUnitAndMassUnit", "StepBasic_SiUnitAndPlaneAngleUnit", "StepBasic_SiUnitAndRatioUnit", "StepBasic_SiUnitAndSolidAngleUnit", "StepBasic_SiUnitAndThermodynamicTemperatureUnit", "StepBasic_SiUnitAndTimeUnit", "StepBasic_SiUnitAndVolumeUnit", "StepBasic_SiUnitName", "StepBasic_SizeMember", "StepBasic_SizeSelect", "StepBasic_SolidAngleMeasureWithUnit", "StepBasic_SolidAngleUnit", "StepBasic_Source", "StepBasic_SourceItem", "StepBasic_ThermodynamicTemperatureUnit", "StepBasic_TimeMeasureWithUnit", "StepBasic_TimeUnit", "StepBasic_UncertaintyMeasureWithUnit", "StepBasic_Unit", "StepBasic_VersionedActionRequest", "StepBasic_VolumeUnit", "StepBasic_WeekOfYearAndDayDate", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepData", "type": "module", "classes": ["StepData_Array1OfField", "StepData_DefaultGeneral", "StepData_Described", "StepData_ECDescr", "StepData_EDescr", "StepData_ESDescr", "StepData_EnumTool", "StepData_Factors", "StepData_Field", "StepData_FieldList", "StepData_FieldList1", "StepData_FieldListD", "StepData_FieldListN", "StepData_FileProtocol", "StepData_FileRecognizer", "StepData_GeneralModule", "StepData_GlobalNodeOfWriterLib", "StepData_HArray1OfField", "StepData_Logical", "StepData_NodeOfWriterLib", "StepData_PDescr", "StepData_Plex", "StepData_Protocol", "StepData_ReadWriteModule", "StepData_SelectArrReal", "StepData_SelectInt", "StepData_SelectMember", "StepData_SelectNamed", "StepData_SelectReal", "StepData_SelectType", "StepData_Simple", "StepData_StepDumper", "StepData_StepModel", "StepData_StepReaderData", "StepData_StepReaderTool", "StepData_StepWriter", "StepData_WriterLib", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stepdata"], "children": []}, {"name": "OCC.Core.StepDimTol", "type": "module", "classes": ["StepDimTol_AngularityTolerance", "StepDimTol_AreaUnitType", "StepDimTol_Array1OfDatumReference", "StepDimTol_Array1OfDatumReferenceCompartment", "StepDimTol_Array1OfDatumReferenceElement", "StepDimTol_Array1OfDatumReferenceModifier", "StepDimTol_Array1OfDatumSystemOrReference", "StepDimTol_Array1OfToleranceZoneTarget", "StepDimTol_CircularRunoutTolerance", "StepDimTol_CoaxialityTolerance", "StepDimTol_CommonDatum", "StepDimTol_ConcentricityTolerance", "StepDimTol_CylindricityTolerance", "StepDimTol_Datum", "StepDimTol_DatumFeature", "StepDimTol_DatumOrCommonDatum", "StepDimTol_DatumReference", "StepDimTol_DatumReferenceCompartment", "StepDimTol_DatumReferenceElement", "StepDimTol_DatumReferenceModifier", "StepDimTol_DatumReferenceModifierType", "StepDimTol_DatumReferenceModifierWithValue", "StepDimTol_DatumSystem", "StepDimTol_DatumSystemOrReference", "StepDimTol_DatumTarget", "StepDimTol_FlatnessTolerance", "StepDimTol_GeneralDatumReference", "StepDimTol_GeoTolAndGeoTolWthDatRef", "StepDimTol_GeoTolAndGeoTolWthDatRefAndGeoTolWthMaxTol", "StepDimTol_GeoTolAndGeoTolWthDatRefAndGeoTolWthMod", "StepDimTol_GeoTolAndGeoTolWthDatRefAndModGeoTolAndPosTol", "StepDimTol_GeoTolAndGeoTolWthDatRefAndUneqDisGeoTol", "StepDimTol_GeoTolAndGeoTolWthMaxTol", "StepDimTol_GeoTolAndGeoTolWthMod", "StepDimTol_GeometricTolerance", "StepDimTol_GeometricToleranceModifier", "StepDimTol_GeometricToleranceRelationship", "StepDimTol_GeometricToleranceTarget", "StepDimTol_GeometricToleranceType", "StepDimTol_GeometricToleranceWithDatumReference", "StepDimTol_GeometricToleranceWithDefinedAreaUnit", "StepDimTol_GeometricToleranceWithDefinedUnit", "StepDimTol_GeometricToleranceWithMaximumTolerance", "StepDimTol_GeometricToleranceWithModifiers", "StepDimTol_HArray1OfDatumReference", "StepDimTol_HArray1OfDatumReferenceCompartment", "StepDimTol_HArray1OfDatumReferenceElement", "StepDimTol_HArray1OfDatumReferenceModifier", "StepDimTol_HArray1OfDatumSystemOrReference", "StepDimTol_HArray1OfGeometricToleranceModifier", "StepDimTol_HArray1OfToleranceZoneTarget", "StepDimTol_LimitCondition", "StepDimTol_LineProfileTolerance", "StepDimTol_ModifiedGeometricTolerance", "StepDimTol_NonUniformZoneDefinition", "StepDimTol_ParallelismTolerance", "StepDimTol_PerpendicularityTolerance", "StepDimTol_PlacedDatumTargetFeature", "StepDimTol_PositionTolerance", "StepDimTol_ProjectedZoneDefinition", "StepDimTol_RoundnessTolerance", "StepDimTol_RunoutZoneDefinition", "StepDimTol_RunoutZoneOrientation", "StepDimTol_ShapeToleranceSelect", "StepDimTol_SimpleDatumReferenceModifier", "StepDimTol_SimpleDatumReferenceModifierMember", "StepDimTol_StraightnessTolerance", "StepDimTol_SurfaceProfileTolerance", "StepDimTol_SymmetryTolerance", "StepDimTol_ToleranceZone", "StepDimTol_ToleranceZoneDefinition", "StepDimTol_ToleranceZoneForm", "StepDimTol_ToleranceZoneTarget", "StepDimTol_TotalRunoutTolerance", "StepDimTol_UnequallyDisposedGeometricTolerance", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepElement", "type": "module", "classes": ["StepElement_AnalysisItemWithinRepresentation", "StepElement_Array1OfCurveElementEndReleasePacket", "StepElement_Array1OfCurveElementSectionDefinition", "StepElement_Array1OfHSequenceOfCurveElementPurposeMember", "StepElement_Array1OfHSequenceOfSurfaceElementPurposeMember", "StepElement_Array1OfMeasureOrUnspecifiedValue", "StepElement_Array1OfSurfaceSection", "StepElement_Array1OfVolumeElementPurpose", "StepElement_Array1OfVolumeElementPurposeMember", "StepElement_Curve3dElementDescriptor", "StepElement_CurveEdge", "StepElement_CurveElementEndReleasePacket", "StepElement_CurveElementFreedom", "StepElement_CurveElementFreedomMember", "StepElement_CurveElementPurpose", "StepElement_CurveElementPurposeMember", "StepElement_CurveElementSectionDefinition", "StepElement_CurveElementSectionDerivedDefinitions", "StepElement_Element2dShape", "StepElement_ElementAspect", "StepElement_ElementAspectMember", "StepElement_ElementDescriptor", "StepElement_ElementMaterial", "StepElement_ElementOrder", "StepElement_ElementVolume", "StepElement_EnumeratedCurveElementFreedom", "StepElement_EnumeratedCurveElementPurpose", "StepElement_EnumeratedSurfaceElementPurpose", "StepElement_EnumeratedVolumeElementPurpose", "StepElement_HArray1OfCurveElementEndReleasePacket", "StepElement_HArray1OfCurveElementSectionDefinition", "StepElement_HArray1OfHSequenceOfCurveElementPurposeMember", "StepElement_HArray1OfHSequenceOfSurfaceElementPurposeMember", "StepElement_HArray1OfMeasureOrUnspecifiedValue", "StepElement_HArray1OfSurfaceSection", "StepElement_HArray1OfVolumeElementPurpose", "StepElement_HArray1OfVolumeElementPurposeMember", "StepElement_HArray2OfCurveElementPurposeMember", "StepElement_HArray2OfSurfaceElementPurpose", "StepElement_HArray2OfSurfaceElementPurposeMember", "StepElement_HSequenceOfCurveElementPurposeMember", "StepElement_HSequenceOfCurveElementSectionDefinition", "StepElement_HSequenceOfElementMaterial", "StepElement_HSequenceOfSurfaceElementPurposeMember", "StepElement_MeasureOrUnspecifiedValue", "StepElement_MeasureOrUnspecifiedValueMember", "StepElement_SequenceOfCurveElementPurposeMember", "StepElement_SequenceOfCurveElementSectionDefinition", "StepElement_SequenceOfElementMaterial", "StepElement_SequenceOfSurfaceElementPurposeMember", "StepElement_Surface3dElementDescriptor", "StepElement_SurfaceElementProperty", "StepElement_SurfaceElementPurpose", "StepElement_SurfaceElementPurposeMember", "StepElement_SurfaceSection", "StepElement_SurfaceSectionField", "StepElement_SurfaceSectionFieldConstant", "StepElement_SurfaceSectionFieldVarying", "StepElement_UniformSurfaceSection", "StepElement_UnspecifiedValue", "StepElement_Volume3dElementDescriptor", "StepElement_Volume3dElementShape", "StepElement_VolumeElementPurpose", "StepElement_VolumeElementPurposeMember", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepFEA", "type": "module", "classes": ["StepFEA_AlignedCurve3dElementCoordinateSystem", "StepFEA_AlignedSurface3dElementCoordinateSystem", "StepFEA_ArbitraryVolume3dElementCoordinateSystem", "StepFEA_Array1OfCurveElementEndOffset", "StepFEA_Array1OfCurveElementEndRelease", "StepFEA_Array1OfCurveElementInterval", "StepFEA_Array1OfDegreeOfFreedom", "StepFEA_Array1OfElementRepresentation", "StepFEA_Array1OfNodeRepresentation", "StepFEA_ConstantSurface3dElementCoordinateSystem", "StepFEA_CoordinateSystemType", "StepFEA_Curve3dElementProperty", "StepFEA_Curve3dElementRepresentation", "StepFEA_CurveEdge", "StepFEA_CurveElementEndCoordinateSystem", "StepFEA_CurveElementEndOffset", "StepFEA_CurveElementEndRelease", "StepFEA_CurveElementInterval", "StepFEA_CurveElementIntervalConstant", "StepFEA_CurveElementIntervalLinearlyVarying", "StepFEA_CurveElementLocation", "StepFEA_DegreeOfFreedom", "StepFEA_DegreeOfFreedomMember", "StepFEA_DummyNode", "StepFEA_ElementGeometricRelationship", "StepFEA_ElementGroup", "StepFEA_ElementOrElementGroup", "StepFEA_ElementRepresentation", "StepFEA_ElementVolume", "StepFEA_EnumeratedDegreeOfFreedom", "StepFEA_FeaAreaDensity", "StepFEA_FeaAxis2Placement3d", "StepFEA_FeaCurveSectionGeometricRelationship", "StepFEA_FeaGroup", "StepFEA_FeaLinearElasticity", "StepFEA_FeaMassDensity", "StepFEA_FeaMaterialPropertyRepresentation", "StepFEA_FeaMaterialPropertyRepresentationItem", "StepFEA_FeaModel", "StepFEA_FeaModel3d", "StepFEA_FeaModelDefinition", "StepFEA_FeaMoistureAbsorption", "StepFEA_FeaParametricPoint", "StepFEA_FeaRepresentationItem", "StepFEA_FeaSecantCoefficientOfLinearThermalExpansion", "StepFEA_FeaShellBendingStiffness", "StepFEA_FeaShellMembraneBendingCouplingStiffness", "StepFEA_FeaShellMembraneStiffness", "StepFEA_FeaShellShearStiffness", "StepFEA_FeaSurfaceSectionGeometricRelationship", "StepFEA_FeaTangentialCoefficientOfLinearThermalExpansion", "StepFEA_FreedomAndCoefficient", "StepFEA_FreedomsList", "StepFEA_GeometricNode", "StepFEA_HArray1OfCurveElementEndOffset", "StepFEA_HArray1OfCurveElementEndRelease", "StepFEA_HArray1OfCurveElementInterval", "StepFEA_HArray1OfDegreeOfFreedom", "StepFEA_HArray1OfElementRepresentation", "StepFEA_HArray1OfNodeRepresentation", "StepFEA_HSequenceOfCurve3dElementProperty", "StepFEA_HSequenceOfElementGeometricRelationship", "StepFEA_HSequenceOfElementRepresentation", "StepFEA_HSequenceOfNodeRepresentation", "StepFEA_Node", "StepFEA_NodeDefinition", "StepFEA_NodeGroup", "StepFEA_NodeRepresentation", "StepFEA_NodeSet", "StepFEA_NodeWithSolutionCoordinateSystem", "StepFEA_NodeWithVector", "StepFEA_ParametricCurve3dElementCoordinateDirection", "StepFEA_ParametricCurve3dElementCoordinateSystem", "StepFEA_ParametricSurface3dElementCoordinateSystem", "StepFEA_SequenceOfCurve3dElementProperty", "StepFEA_SequenceOfElementGeometricRelationship", "StepFEA_SequenceOfElementRepresentation", "StepFEA_SequenceOfNodeRepresentation", "StepFEA_Surface3dElementRepresentation", "StepFEA_SymmetricTensor22d", "StepFEA_SymmetricTensor23d", "StepFEA_SymmetricTensor23dMember", "StepFEA_SymmetricTensor42d", "StepFEA_SymmetricTensor43d", "StepFEA_SymmetricTensor43dMember", "StepFEA_UnspecifiedValue", "StepFEA_Volume3dElementRepresentation", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepGeom", "type": "module", "classes": ["StepGeom_Array1OfBoundaryCurve", "StepGeom_Array1OfCartesianPoint", "StepGeom_Array1OfCompositeCurveSegment", "StepGeom_Array1OfCurve", "StepGeom_Array1OfPcurveOrSurface", "StepGeom_Array1OfSurfaceBoundary", "StepGeom_Array1OfTrimmingSelect", "StepGeom_Axis1Placement", "StepGeom_Axis2Placement", "StepGeom_Axis2Placement2d", "StepGeom_Axis2Placement3d", "StepGeom_BSplineCurve", "StepGeom_BSplineCurveForm", "StepGeom_BSplineCurveWithKnots", "StepGeom_BSplineCurveWithKnotsAndRationalBSplineCurve", "StepGeom_BSplineSurface", "StepGeom_BSplineSurfaceForm", "StepGeom_BSplineSurfaceWithKnots", "StepGeom_BSplineSurfaceWithKnotsAndRationalBSplineSurface", "StepGeom_BezierCurve", "StepGeom_BezierCurveAndRationalBSplineCurve", "StepGeom_BezierSurface", "StepGeom_BezierSurfaceAndRationalBSplineSurface", "StepGeom_BoundaryCurve", "StepGeom_BoundedCurve", "StepGeom_BoundedSurface", "StepGeom_CartesianPoint", "StepGeom_CartesianTransformationOperator", "StepGeom_CartesianTransformationOperator2d", "StepGeom_CartesianTransformationOperator3d", "StepGeom_Circle", "StepGeom_CompositeCurve", "StepGeom_CompositeCurveOnSurface", "StepGeom_CompositeCurveSegment", "StepGeom_Conic", "StepGeom_ConicalSurface", "StepGeom_Curve", "StepGeom_CurveBoundedSurface", "StepGeom_CurveOnSurface", "StepGeom_CurveReplica", "StepGeom_CylindricalSurface", "StepGeom_DegeneratePcurve", "StepGeom_DegenerateToroidalSurface", "StepGeom_Direction", "StepGeom_ElementarySurface", "StepGeom_Ellipse", "StepGeom_EvaluatedDegeneratePcurve", "StepGeom_GeomRepContextAndGlobUnitAssCtxAndGlobUncertaintyAssCtx", "StepGeom_GeometricRepresentationContext", "StepGeom_GeometricRepresentationContextAndGlobalUnitAssignedContext", "StepGeom_GeometricRepresentationContextAndParametricRepresentationContext", "StepGeom_GeometricRepresentationItem", "StepGeom_HArray1OfBoundaryCurve", "StepGeom_HArray1OfCartesianPoint", "StepGeom_HArray1OfCompositeCurveSegment", "StepGeom_HArray1OfCurve", "StepGeom_HArray1OfPcurveOrSurface", "StepGeom_HArray1OfSurfaceBoundary", "StepGeom_HArray1OfTrimmingSelect", "StepGeom_HArray2OfCartesianPoint", "StepGeom_HArray2OfSurfacePatch", "StepGeom_Hyperbola", "StepGeom_IntersectionCurve", "StepGeom_KnotType", "StepGeom_Line", "StepGeom_OffsetCurve3d", "StepGeom_OffsetSurface", "StepGeom_OrientedSurface", "StepGeom_OuterBoundaryCurve", "StepGeom_Parabola", "StepGeom_Pcurve", "StepGeom_PcurveOrSurface", "StepGeom_Placement", "StepGeom_Plane", "StepGeom_Point", "StepGeom_PointOnCurve", "StepGeom_PointOnSurface", "StepGeom_PointReplica", "StepGeom_Polyline", "StepGeom_PreferredSurfaceCurveRepresentation", "StepGeom_QuasiUniformCurve", "StepGeom_QuasiUniformCurveAndRationalBSplineCurve", "StepGeom_QuasiUniformSurface", "StepGeom_QuasiUniformSurfaceAndRationalBSplineSurface", "StepGeom_RationalBSplineCurve", "StepGeom_RationalBSplineSurface", "StepGeom_RectangularCompositeSurface", "StepGeom_RectangularTrimmedSurface", "StepGeom_ReparametrisedCompositeCurveSegment", "StepGeom_SeamCurve", "StepGeom_SphericalSurface", "StepGeom_SuParameters", "StepGeom_Surface", "StepGeom_SurfaceBoundary", "StepGeom_SurfaceCurve", "StepGeom_SurfaceCurveAndBoundedCurve", "StepGeom_SurfaceOfLinearExtrusion", "StepGeom_SurfaceOfRevolution", "StepGeom_SurfacePatch", "StepGeom_SurfaceReplica", "StepGeom_SweptSurface", "StepGeom_ToroidalSurface", "StepGeom_TransitionCode", "StepGeom_TrimmedCurve", "StepGeom_TrimmingMember", "StepGeom_TrimmingPreference", "StepGeom_TrimmingSelect", "StepGeom_UniformCurve", "StepGeom_UniformCurveAndRationalBSplineCurve", "StepGeom_UniformSurface", "StepGeom_UniformSurfaceAndRationalBSplineSurface", "StepGeom_Vector", "StepGeom_VectorOrDirection", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepKinematics", "type": "module", "classes": ["StepKinematics_ActuatedDirection", "StepKinematics_ActuatedKinPairAndOrderKinPair", "StepKinematics_ActuatedKinematicPair", "StepKinematics_ContextDependentKinematicLinkRepresentation", "StepKinematics_CylindricalPair", "StepKinematics_CylindricalPairValue", "StepKinematics_CylindricalPairWithRange", "StepKinematics_FullyConstrainedPair", "StepKinematics_GearPair", "StepKinematics_GearPairValue", "StepKinematics_GearPairWithRange", "StepKinematics_HighOrderKinematicPair", "StepKinematics_HomokineticPair", "StepKinematics_KinematicJoint", "StepKinematics_KinematicLink", "StepKinematics_KinematicLinkRepresentation", "StepKinematics_KinematicLinkRepresentationAssociation", "StepKinematics_KinematicPair", "StepKinematics_KinematicPropertyDefinitionRepresentation", "StepKinematics_KinematicPropertyMechanismRepresentation", "StepKinematics_KinematicTopologyDirectedStructure", "StepKinematics_KinematicTopologyNetworkStructure", "StepKinematics_KinematicTopologyRepresentationSelect", "StepKinematics_KinematicTopologyStructure", "StepKinematics_LinearFlexibleAndPinionPair", "StepKinematics_LinearFlexibleAndPlanarCurvePair", "StepKinematics_LinearFlexibleLinkRepresentation", "StepKinematics_LowOrderKinematicPair", "StepKinematics_LowOrderKinematicPairValue", "StepKinematics_LowOrderKinematicPairWithMotionCoupling", "StepKinematics_LowOrderKinematicPairWithRange", "StepKinematics_MechanismRepresentation", "StepKinematics_MechanismStateRepresentation", "StepKinematics_OrientedJoint", "StepKinematics_PairRepresentationRelationship", "StepKinematics_PairValue", "StepKinematics_PlanarCurvePair", "StepKinematics_PlanarCurvePairRange", "StepKinematics_PlanarPair", "StepKinematics_PlanarPairValue", "StepKinematics_PlanarPairWithRange", "StepKinematics_PointOnPlanarCurvePair", "StepKinematics_PointOnPlanarCurvePairValue", "StepKinematics_PointOnPlanarCurvePairWithRange", "StepKinematics_PointOnSurfacePair", "StepKinematics_PointOnSurfacePairValue", "StepKinematics_PointOnSurfacePairWithRange", "StepKinematics_PrismaticPair", "StepKinematics_PrismaticPairValue", "StepKinematics_PrismaticPairWithRange", "StepKinematics_ProductDefinitionKinematics", "StepKinematics_ProductDefinitionRelationshipKinematics", "StepKinematics_RackAndPinionPair", "StepKinematics_RackAndPinionPairValue", "StepKinematics_RackAndPinionPairWithRange", "StepKinematics_RevolutePair", "StepKinematics_RevolutePairValue", "StepKinematics_RevolutePairWithRange", "StepKinematics_RigidLinkRepresentation", "StepKinematics_RigidPlacement", "StepKinematics_RollingCurvePair", "StepKinematics_RollingCurvePairValue", "StepKinematics_RollingSurfacePair", "StepKinematics_RollingSurfacePairValue", "StepKinematics_RotationAboutDirection", "StepKinematics_ScrewPair", "StepKinematics_ScrewPairValue", "StepKinematics_ScrewPairWithRange", "StepKinematics_SlidingCurvePair", "StepKinematics_SlidingCurvePairValue", "StepKinematics_SlidingSurfacePair", "StepKinematics_SlidingSurfacePairValue", "StepKinematics_SpatialRotation", "StepKinematics_SphericalPair", "StepKinematics_SphericalPairSelect", "StepKinematics_SphericalPairValue", "StepKinematics_SphericalPairWithPin", "StepKinematics_SphericalPairWithPinAndRange", "StepKinematics_SphericalPairWithRange", "StepKinematics_SurfacePair", "StepKinematics_SurfacePairWithRange", "StepKinematics_UnconstrainedPair", "StepKinematics_UnconstrainedPairValue", "StepKinematics_UniversalPair", "StepKinematics_UniversalPairValue", "StepKinematics_UniversalPairWithRange", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepRepr", "type": "module", "classes": ["StepRepr_AllAroundShapeAspect", "StepRepr_Apex", "StepRepr_Array1OfMaterialPropertyRepresentation", "StepRepr_Array1OfPropertyDefinitionRepresentation", "StepRepr_Array1OfRepresentationItem", "StepRepr_Array1OfShapeAspect", "StepRepr_AssemblyComponentUsage", "StepRepr_AssemblyComponentUsageSubstitute", "StepRepr_BetweenShapeAspect", "StepRepr_BooleanRepresentationItem", "StepRepr_CentreOfSymmetry", "StepRepr_CharacterizedDefinition", "StepRepr_CharacterizedRepresentation", "StepRepr_CompGroupShAspAndCompShAspAndDatumFeatAndShAsp", "StepRepr_CompShAspAndDatumFeatAndShAsp", "StepRepr_CompositeGroupShapeAspect", "StepRepr_CompositeShapeAspect", "StepRepr_CompoundRepresentationItem", "StepRepr_ConfigurationDesign", "StepRepr_ConfigurationDesignItem", "StepRepr_ConfigurationEffectivity", "StepRepr_ConfigurationItem", "StepRepr_ConstructiveGeometryRepresentation", "StepRepr_ConstructiveGeometryRepresentationRelationship", "StepRepr_ContinuosShapeAspect", "StepRepr_DataEnvironment", "StepRepr_DefinitionalRepresentation", "StepRepr_DerivedShapeAspect", "StepRepr_DescriptiveRepresentationItem", "StepRepr_Extension", "StepRepr_ExternallyDefinedRepresentation", "StepRepr_FeatureForDatumTargetRelationship", "StepRepr_FunctionallyDefinedTransformation", "StepRepr_GeometricAlignment", "StepRepr_GlobalUncertaintyAssignedContext", "StepRepr_GlobalUnitAssignedContext", "StepRepr_HArray1OfMaterialPropertyRepresentation", "StepRepr_HArray1OfPropertyDefinitionRepresentation", "StepRepr_HArray1OfRepresentationItem", "StepRepr_HArray1OfShapeAspect", "StepRepr_HSequenceOfMaterialPropertyRepresentation", "StepRepr_HSequenceOfRepresentationItem", "StepRepr_IntegerRepresentationItem", "StepRepr_ItemDefinedTransformation", "StepRepr_MakeFromUsageOption", "StepRepr_MappedItem", "StepRepr_MaterialDesignation", "StepRepr_MaterialProperty", "StepRepr_MaterialPropertyRepresentation", "StepRepr_MeasureRepresentationItem", "StepRepr_MechanicalDesignAndDraughtingRelationship", "StepRepr_NextAssemblyUsageOccurrence", "StepRepr_ParallelOffset", "StepRepr_ParametricRepresentationContext", "StepRepr_PerpendicularTo", "StepRepr_ProductConcept", "StepRepr_ProductDefinitionShape", "StepRepr_ProductDefinitionUsage", "StepRepr_PromissoryUsageOccurrence", "StepRepr_PropertyDefinition", "StepRepr_PropertyDefinitionRelationship", "StepRepr_PropertyDefinitionRepresentation", "StepRepr_QuantifiedAssemblyComponentUsage", "StepRepr_RealRepresentationItem", "StepRepr_ReprItemAndLengthMeasureWithUnit", "StepRepr_ReprItemAndLengthMeasureWithUnitAndQRI", "StepRepr_ReprItemAndMeasureWithUnit", "StepRepr_ReprItemAndPlaneAngleMeasureWithUnit", "StepRepr_ReprItemAndPlaneAngleMeasureWithUnitAndQRI", "StepRepr_Representation", "StepRepr_RepresentationContext", "StepRepr_RepresentationContextReference", "StepRepr_RepresentationItem", "StepRepr_RepresentationMap", "StepRepr_RepresentationOrRepresentationReference", "StepRepr_RepresentationReference", "StepRepr_RepresentationRelationship", "StepRepr_RepresentationRelationshipWithTransformation", "StepRepr_RepresentedDefinition", "StepRepr_SequenceOfMaterialPropertyRepresentation", "StepRepr_SequenceOfRepresentationItem", "StepRepr_ShapeAspect", "StepRepr_ShapeAspectDerivingRelationship", "StepRepr_ShapeAspectRelationship", "StepRepr_ShapeAspectTransition", "StepRepr_ShapeDefinition", "StepRepr_ShapeRepresentationRelationship", "StepRepr_ShapeRepresentationRelationshipWithTransformation", "StepRepr_SpecifiedHigherUsageOccurrence", "StepRepr_StructuralResponseProperty", "StepRepr_StructuralResponsePropertyDefinitionRepresentation", "StepRepr_SuppliedPartRelationship", "StepRepr_Tangent", "StepRepr_Transformation", "StepRepr_ValueRange", "StepRepr_ValueRepresentationItem", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepShape", "type": "module", "classes": ["StepShape_AdvancedBrepShapeRepresentation", "StepShape_AdvancedFace", "StepShape_AngleRelator", "StepShape_AngularLocation", "StepShape_AngularSize", "StepShape_Array1OfConnectedEdgeSet", "StepShape_Array1OfConnectedFaceSet", "StepShape_Array1OfEdge", "StepShape_Array1OfFace", "StepShape_Array1OfFaceBound", "StepShape_Array1OfGeometricSetSelect", "StepShape_Array1OfOrientedClosedShell", "StepShape_Array1OfOrientedEdge", "StepShape_Array1OfShapeDimensionRepresentationItem", "StepShape_Array1OfShell", "StepShape_Array1OfValueQualifier", "StepShape_Block", "StepShape_BooleanOperand", "StepShape_BooleanOperator", "StepShape_BooleanResult", "StepShape_BoxDomain", "StepShape_BoxedHalfSpace", "StepShape_BrepWithVoids", "StepShape_ClosedShell", "StepShape_CompoundShapeRepresentation", "StepShape_ConnectedEdgeSet", "StepShape_ConnectedFaceSet", "StepShape_ConnectedFaceShapeRepresentation", "StepShape_ConnectedFaceSubSet", "StepShape_ContextDependentShapeRepresentation", "StepShape_CsgPrimitive", "StepShape_CsgSelect", "StepShape_CsgShapeRepresentation", "StepShape_CsgSolid", "StepShape_DefinitionalRepresentationAndShapeRepresentation", "StepShape_DimensionalCharacteristic", "StepShape_DimensionalCharacteristicRepresentation", "StepShape_DimensionalLocation", "StepShape_DimensionalLocationWithPath", "StepShape_DimensionalSize", "StepShape_DimensionalSizeWithPath", "StepShape_DirectedDimensionalLocation", "StepShape_Edge", "StepShape_EdgeBasedWireframeModel", "StepShape_EdgeBasedWireframeShapeRepresentation", "StepShape_EdgeCurve", "StepShape_EdgeLoop", "StepShape_ExtrudedAreaSolid", "StepShape_ExtrudedFaceSolid", "StepShape_Face", "StepShape_FaceBasedSurfaceModel", "StepShape_FaceBound", "StepShape_FaceOuterBound", "StepShape_FaceSurface", "StepShape_FacetedBrep", "StepShape_FacetedBrepAndBrepWithVoids", "StepShape_FacetedBrepShapeRepresentation", "StepShape_GeometricCurveSet", "StepShape_GeometricSet", "StepShape_GeometricSetSelect", "StepShape_GeometricallyBoundedSurfaceShapeRepresentation", "StepShape_GeometricallyBoundedWireframeShapeRepresentation", "StepShape_HArray1OfConnectedEdgeSet", "StepShape_HArray1OfConnectedFaceSet", "StepShape_HArray1OfEdge", "StepShape_HArray1OfFace", "StepShape_HArray1OfFaceBound", "StepShape_HArray1OfGeometricSetSelect", "StepShape_HArray1OfOrientedClosedShell", "StepShape_HArray1OfOrientedEdge", "StepShape_HArray1OfShapeDimensionRepresentationItem", "StepShape_HArray1OfShell", "StepShape_HArray1OfValueQualifier", "StepShape_HalfSpaceSolid", "StepShape_LimitsAndFits", "StepShape_Loop", "StepShape_LoopAndPath", "StepShape_ManifoldSolidBrep", "StepShape_ManifoldSurfaceShapeRepresentation", "StepShape_MeasureQualification", "StepShape_MeasureRepresentationItemAndQualifiedRepresentationItem", "StepShape_NonManifoldSurfaceShapeRepresentation", "StepShape_OpenShell", "StepShape_OrientedClosedShell", "StepShape_OrientedEdge", "StepShape_OrientedFace", "StepShape_OrientedOpenShell", "StepShape_OrientedPath", "StepShape_Path", "StepShape_PlusMinusTolerance", "StepShape_PointRepresentation", "StepShape_PolyLoop", "StepShape_PrecisionQualifier", "StepShape_QualifiedRepresentationItem", "StepShape_ReversibleTopologyItem", "StepShape_RevolvedAreaSolid", "StepShape_RevolvedFaceSolid", "StepShape_RightAngularWedge", "StepShape_RightCircularCone", "StepShape_RightCircularCylinder", "StepShape_SeamEdge", "StepShape_ShapeDefinitionRepresentation", "StepShape_ShapeDimensionRepresentation", "StepShape_ShapeDimensionRepresentationItem", "StepShape_ShapeRepresentation", "StepShape_ShapeRepresentationWithParameters", "StepShape_Shell", "StepShape_ShellBasedSurfaceModel", "StepShape_SolidModel", "StepShape_SolidReplica", "StepShape_Sphere", "StepShape_Subedge", "StepShape_Subface", "StepShape_SurfaceModel", "StepShape_SweptAreaSolid", "StepShape_SweptFaceSolid", "StepShape_ToleranceMethodDefinition", "StepShape_ToleranceValue", "StepShape_TopologicalRepresentationItem", "StepShape_Torus", "StepShape_TransitionalShapeRepresentation", "StepShape_TypeQualifier", "StepShape_ValueFormatTypeQualifier", "StepShape_ValueQualifier", "StepShape_Vertex", "StepShape_VertexLoop", "StepShape_VertexPoint", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StepToGeom", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "steptogeom"], "children": []}, {"name": "OCC.Core.StepToTopoDS", "type": "module", "classes": ["StepToTopoDS_BuilderError", "StepToTopoDS_DataMapOfRI", "StepToTopoDS_DataMapOfRINames", "StepToTopoDS_DataMapOfTRI", "StepToTopoDS_GeometricTool", "StepToTopoDS_GeometricToolError", "StepToTopoDS_MakeTransformed", "StepToTopoDS_NMTool", "StepToTopoDS_PointEdgeMap", "StepToTopoDS_PointPair", "StepToTopoDS_Root", "StepToTopoDS_Tool", "StepToTopoDS_TranslateCompositeCurve", "StepToTopoDS_TranslateCurveBoundedSurface", "StepToTopoDS_TranslateEdge", "StepToTopoDS_TranslateEdgeError", "StepToTopoDS_TranslateEdgeLoop", "StepToTopoDS_TranslateEdgeLoopError", "StepToTopoDS_TranslateFace", "StepToTopoDS_TranslateFaceError", "StepToTopoDS_TranslatePolyLoop", "StepToTopoDS_TranslatePolyLoopError", "StepToTopoDS_TranslateShell", "StepToTopoDS_TranslateShellError", "StepToTopoDS_TranslateSolid", "StepToTopoDS_TranslateSolidError", "StepToTopoDS_TranslateVertex", "StepToTopoDS_TranslateVertexError", "StepToTopoDS_TranslateVertexLoop", "StepToTopoDS_TranslateVertexLoopError", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "steptotopods"], "children": []}, {"name": "OCC.Core.StepVisual", "type": "module", "classes": ["StepVisual_AnnotationCurveOccurrence", "StepVisual_AnnotationCurveOccurrenceAndGeomReprItem", "StepVisual_AnnotationFillArea", "StepVisual_AnnotationFillAreaOccurrence", "StepVisual_AnnotationOccurrence", "StepVisual_AnnotationPlane", "StepVisual_AnnotationPlaneElement", "StepVisual_AnnotationText", "StepVisual_AnnotationTextOccurrence", "StepVisual_AreaInSet", "StepVisual_AreaOrView", "StepVisual_Array1OfAnnotationPlaneElement", "StepVisual_Array1OfBoxCharacteristicSelect", "StepVisual_Array1OfCameraModelD3MultiClippingInterectionSelect", "StepVisual_Array1OfCameraModelD3MultiClippingUnionSelect", "StepVisual_Array1OfCurveStyleFontPattern", "StepVisual_Array1OfDirectionCountSelect", "StepVisual_Array1OfDraughtingCalloutElement", "StepVisual_Array1OfFillStyleSelect", "StepVisual_Array1OfInvisibleItem", "StepVisual_Array1OfLayeredItem", "StepVisual_Array1OfPresentationStyleAssignment", "StepVisual_Array1OfPresentationStyleSelect", "StepVisual_Array1OfRenderingPropertiesSelect", "StepVisual_Array1OfStyleContextSelect", "StepVisual_Array1OfSurfaceStyleElementSelect", "StepVisual_Array1OfTessellatedEdgeOrVertex", "StepVisual_Array1OfTessellatedItem", "StepVisual_Array1OfTessellatedStructuredItem", "StepVisual_Array1OfTextOrCharacter", "StepVisual_BackgroundColour", "StepVisual_BoxCharacteristicSelect", "StepVisual_CameraImage", "StepVisual_CameraImage2dWithScale", "StepVisual_CameraImage3dWithScale", "StepVisual_CameraModel", "StepVisual_CameraModelD2", "StepVisual_CameraModelD3", "StepVisual_CameraModelD3MultiClipping", "StepVisual_CameraModelD3MultiClippingInterectionSelect", "StepVisual_CameraModelD3MultiClippingIntersection", "StepVisual_CameraModelD3MultiClippingUnion", "StepVisual_CameraModelD3MultiClippingUnionSelect", "StepVisual_CameraUsage", "StepVisual_CentralOrParallel", "StepVisual_CharacterizedObjAndRepresentationAndDraughtingModel", "StepVisual_Colour", "StepVisual_ColourRgb", "StepVisual_ColourSpecification", "StepVisual_ComplexTriangulatedFace", "StepVisual_ComplexTriangulatedSurfaceSet", "StepVisual_CompositeText", "StepVisual_CompositeTextWithExtent", "StepVisual_ContextDependentInvisibility", "StepVisual_ContextDependentOverRidingStyledItem", "StepVisual_CoordinatesList", "StepVisual_CubicBezierTessellatedEdge", "StepVisual_CubicBezierTriangulatedFace", "StepVisual_CurveStyle", "StepVisual_CurveStyleFont", "StepVisual_CurveStyleFontPattern", "StepVisual_CurveStyleFontSelect", "StepVisual_DirectionCountSelect", "StepVisual_DraughtingAnnotationOccurrence", "StepVisual_DraughtingCallout", "StepVisual_DraughtingCalloutElement", "StepVisual_DraughtingModel", "StepVisual_DraughtingPreDefinedColour", "StepVisual_DraughtingPreDefinedCurveFont", "StepVisual_EdgeOrCurve", "StepVisual_ExternallyDefinedCurveFont", "StepVisual_ExternallyDefinedTextFont", "StepVisual_FaceOrSurface", "StepVisual_FillAreaStyle", "StepVisual_FillAreaStyleColour", "StepVisual_FillStyleSelect", "StepVisual_FontSelect", "StepVisual_HArray1OfAnnotationPlaneElement", "StepVisual_HArray1OfBoxCharacteristicSelect", "StepVisual_HArray1OfCameraModelD3MultiClippingInterectionSelect", "StepVisual_HArray1OfCameraModelD3MultiClippingUnionSelect", "StepVisual_HArray1OfCurveStyleFontPattern", "StepVisual_HArray1OfDirectionCountSelect", "StepVisual_HArray1OfDraughtingCalloutElement", "StepVisual_HArray1OfFillStyleSelect", "StepVisual_HArray1OfInvisibleItem", "StepVisual_HArray1OfLayeredItem", "StepVisual_HArray1OfPresentationStyleAssignment", "StepVisual_HArray1OfPresentationStyleSelect", "StepVisual_HArray1OfRenderingPropertiesSelect", "StepVisual_HArray1OfStyleContextSelect", "StepVisual_HArray1OfSurfaceStyleElementSelect", "StepVisual_HArray1OfTessellatedEdgeOrVertex", "StepVisual_HArray1OfTessellatedStructuredItem", "StepVisual_HArray1OfTextOrCharacter", "StepVisual_Invisibility", "StepVisual_InvisibilityContext", "StepVisual_InvisibleItem", "StepVisual_LayeredItem", "StepVisual_MarkerMember", "StepVisual_MarkerSelect", "StepVisual_MarkerType", "StepVisual_MechanicalDesignGeometricPresentationArea", "StepVisual_MechanicalDesignGeometricPresentationRepresentation", "StepVisual_NullStyle", "StepVisual_NullStyleMember", "StepVisual_OverRidingStyledItem", "StepVisual_PathOrCompositeCurve", "StepVisual_PlanarBox", "StepVisual_PlanarExtent", "StepVisual_PointStyle", "StepVisual_PreDefinedColour", "StepVisual_PreDefinedCurveFont", "StepVisual_PreDefinedItem", "StepVisual_PreDefinedTextFont", "StepVisual_PresentationArea", "StepVisual_PresentationLayerAssignment", "StepVisual_PresentationLayerUsage", "StepVisual_PresentationRepresentation", "StepVisual_PresentationRepresentationSelect", "StepVisual_PresentationSet", "StepVisual_PresentationSize", "StepVisual_PresentationSizeAssignmentSelect", "StepVisual_PresentationStyleAssignment", "StepVisual_PresentationStyleByContext", "StepVisual_PresentationStyleSelect", "StepVisual_PresentationView", "StepVisual_PresentedItem", "StepVisual_PresentedItemRepresentation", "StepVisual_RenderingPropertiesSelect", "StepVisual_RepositionedTessellatedGeometricSet", "StepVisual_RepositionedTessellatedItem", "StepVisual_ShadingSurfaceMethod", "StepVisual_StyleContextSelect", "StepVisual_StyledItem", "StepVisual_StyledItemTarget", "StepVisual_SurfaceSide", "StepVisual_SurfaceSideStyle", "StepVisual_SurfaceStyleBoundary", "StepVisual_SurfaceStyleControlGrid", "StepVisual_SurfaceStyleElementSelect", "StepVisual_SurfaceStyleFillArea", "StepVisual_SurfaceStyleParameterLine", "StepVisual_SurfaceStyleReflectanceAmbient", "StepVisual_SurfaceStyleRendering", "StepVisual_SurfaceStyleRenderingWithProperties", "StepVisual_SurfaceStyleSegmentationCurve", "StepVisual_SurfaceStyleSilhouette", "StepVisual_SurfaceStyleTransparent", "StepVisual_SurfaceStyleUsage", "StepVisual_Template", "StepVisual_TemplateInstance", "StepVisual_TessellatedAnnotationOccurrence", "StepVisual_TessellatedConnectingEdge", "StepVisual_TessellatedCurveSet", "StepVisual_TessellatedEdge", "StepVisual_TessellatedEdgeOrVertex", "StepVisual_TessellatedFace", "StepVisual_TessellatedGeometricSet", "StepVisual_TessellatedItem", "StepVisual_TessellatedPointSet", "StepVisual_TessellatedShapeRepresentation", "StepVisual_TessellatedShapeRepresentationWithAccuracyParameters", "StepVisual_TessellatedShell", "StepVisual_TessellatedSolid", "StepVisual_TessellatedStructuredItem", "StepVisual_TessellatedSurfaceSet", "StepVisual_TessellatedVertex", "StepVisual_TessellatedWire", "StepVisual_TextLiteral", "StepVisual_TextOrCharacter", "StepVisual_TextPath", "StepVisual_TextStyle", "StepVisual_TextStyleForDefinedFont", "StepVisual_TextStyleWithBoxCharacteristics", "StepVisual_TriangulatedFace", "StepVisual_TriangulatedSurfaceSet", "StepVisual_ViewVolume", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.StlAPI", "type": "module", "classes": ["StlAPI_Reader", "StlAPI_Writer", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "stlapi"], "children": []}, {"name": "OCC.Core.Storage", "type": "module", "classes": ["Storage_ArrayOfCallBack", "Storage_ArrayOfSchema", "Storage_Error", "Storage_HArrayOfCallBack", "Storage_HArrayOfSchema", "Storage_HPArray", "Storage_HSeqOfRoot", "Storage_MapOfCallBack", "Storage_MapOfPers", "Storage_OpenMode", "Storage_PType", "Storage_SeqOfRoot", "Storage_SolveMode", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Sweep", "type": "module", "classes": ["Sweep_NumShape", "Sweep_NumShapeIterator", "Sweep_NumShapeTool", "SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TColGeom", "type": "module", "classes": ["SwigPyIterator", "TColGeom_Array1OfBSplineCurve", "TColGeom_Array1OfBezierCurve", "TColGeom_Array1OfCurve", "TColGeom_Array1OfSurface", "TColGeom_Array2OfSurface", "TColGeom_HArray1OfBSplineCurve", "TColGeom_HArray1OfBezierCurve", "TColGeom_HArray1OfCurve", "TColGeom_HArray1OfSurface", "TColGeom_HArray2OfSurface", "TColGeom_HSequenceOfBoundedCurve", "TColGeom_HSequenceOfCurve", "TColGeom_SequenceOfBoundedCurve", "TColGeom_SequenceOfCurve", "TColGeom_SequenceOfSurface", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TColGeom2d", "type": "module", "classes": ["SwigPyIterator", "TColGeom2d_Array1OfBSplineCurve", "TColGeom2d_Array1OfBezierCurve", "TColGeom2d_Array1OfCurve", "TColGeom2d_HArray1OfBSplineCurve", "TColGeom2d_HArray1OfBezierCurve", "TColGeom2d_HArray1OfCurve", "TColGeom2d_HSequenceOfBoundedCurve", "TColGeom2d_HSequenceOfCurve", "TColGeom2d_SequenceOfBoundedCurve", "TColGeom2d_SequenceOfCurve", "TColGeom2d_SequenceOfGeometry", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TColQuantity", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TColStd", "type": "module", "classes": ["SwigPyIterator", "TColStd_Array1OfAsciiString", "TColStd_Array1OfBoolean", "TColStd_Array1OfByte", "TColStd_Array1OfCharacter", "TColStd_Array1OfExtendedString", "TColStd_Array1OfInteger", "TColStd_Array1OfReal", "TColStd_Array1OfTransient", "TColStd_Array2OfBoolean", "TColStd_Array2OfCharacter", "TColStd_Array2OfInteger", "TColStd_Array2OfReal", "TColStd_Array2OfTransient", "TColStd_DataMapOfAsciiStringInteger", "TColStd_DataMapOfIntegerInteger", "TColStd_DataMapOfIntegerListOfInteger", "TColStd_DataMapOfIntegerReal", "TColStd_DataMapOfIntegerTransient", "TColStd_DataMapOfStringInteger", "TColStd_DataMapOfTransientTransient", "TColStd_HArray1OfAsciiString", "TColStd_HArray1OfBoolean", "TColStd_HArray1OfByte", "TColStd_HArray1OfCharacter", "TColStd_HArray1OfExtendedString", "TColStd_HArray1OfInteger", "TColStd_HArray1OfListOfInteger", "TColStd_HArray1OfReal", "TColStd_HArray1OfTransient", "TColStd_HArray2OfBoolean", "TColStd_HArray2OfCharacter", "TColStd_HArray2OfInteger", "TColStd_HArray2OfReal", "TColStd_HArray2OfTransient", "TColStd_HPackedMapOfInteger", "TColStd_HSequenceOfAsciiString", "TColStd_HSequenceOfExtendedString", "TColStd_HSequenceOfHAsciiString", "TColStd_HSequenceOfHExtendedString", "TColStd_HSequenceOfInteger", "TColStd_HSequenceOfReal", "TColStd_HSequenceOfTransient", "TColStd_IndexedDataMapOfStringString", "TColStd_IndexedDataMapOfTransientTransient", "TColStd_IndexedMapOfInteger", "TColStd_IndexedMapOfReal", "TColStd_IndexedMapOfTransient", "TColStd_ListIteratorOfListOfAsciiString", "TColStd_ListIteratorOfListOfInteger", "TColStd_ListIteratorOfListOfReal", "TColStd_ListIteratorOfListOfTransient", "TColStd_ListOfAsciiString", "TColStd_ListOfInteger", "TColStd_ListOfReal", "TColStd_ListOfTransient", "TColStd_MapOfAsciiString", "TColStd_MapOfInteger", "TColStd_MapOfReal", "TColStd_MapOfTransient", "TColStd_PackedMapOfInteger", "TColStd_SequenceOfAddress", "TColStd_SequenceOfAsciiString", "TColStd_SequenceOfBoolean", "TColStd_SequenceOfExtendedString", "TColStd_SequenceOfHAsciiString", "TColStd_SequenceOfHExtendedString", "TColStd_SequenceOfInteger", "TColStd_SequenceOfReal", "TColStd_SequenceOfTransient", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TColgp", "type": "module", "classes": ["SwigPyIterator", "TColgp_Array1OfCirc2d", "TColgp_Array1OfDir", "TColgp_Array1OfDir2d", "TColgp_Array1OfLin2d", "TColgp_Array1OfPnt", "TColgp_Array1OfPnt2d", "TColgp_Array1OfVec", "TColgp_Array1OfVec2d", "TColgp_Array1OfXY", "TColgp_Array1OfXYZ", "TColgp_Array2OfCirc2d", "TColgp_Array2OfDir", "TColgp_Array2OfDir2d", "TColgp_Array2OfLin2d", "TColgp_Array2OfPnt", "TColgp_Array2OfPnt2d", "TColgp_Array2OfVec", "TColgp_Array2OfVec2d", "TColgp_Array2OfXY", "TColgp_Array2OfXYZ", "TColgp_HArray1OfCirc2d", "TColgp_HArray1OfDir", "TColgp_HArray1OfDir2d", "TColgp_HArray1OfLin2d", "TColgp_HArray1OfPnt", "TColgp_HArray1OfPnt2d", "TColgp_HArray1OfVec", "TColgp_HArray1OfVec2d", "TColgp_HArray1OfXY", "TColgp_HArray1OfXYZ", "TColgp_HArray2OfCirc2d", "TColgp_HArray2OfDir", "TColgp_HArray2OfDir2d", "TColgp_HArray2OfLin2d", "TColgp_HArray2OfPnt", "TColgp_HArray2OfPnt2d", "TColgp_HArray2OfVec", "TColgp_HArray2OfVec2d", "TColgp_HArray2OfXY", "TColgp_HArray2OfXYZ", "TColgp_HSequenceOfDir", "TColgp_HSequenceOfDir2d", "TColgp_HSequenceOfPnt", "TColgp_HSequenceOfPnt2d", "TColgp_HSequenceOfVec", "TColgp_HSequenceOfVec2d", "TColgp_HSequenceOfXY", "TColgp_HSequenceOfXYZ", "TColgp_SequenceOfArray1OfPnt2d", "TColgp_SequenceOfAx1", "TColgp_SequenceOfDir", "TColgp_SequenceOfDir2d", "TColgp_SequenceOfPnt", "TColgp_SequenceOfPnt2d", "TColgp_SequenceOfVec", "TColgp_SequenceOfVec2d", "TColgp_SequenceOfXY", "TColgp_SequenceOfXYZ", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TCollection", "type": "module", "classes": ["SwigPyIterator", "TCollection_AsciiString", "TCollection_ExtendedString", "TCollection_HAsciiString", "TCollection_HExtendedString", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tcollection"], "children": []}, {"name": "OCC.Core.TDF", "type": "module", "classes": ["SwigPyIterator", "TDF_Attribute", "TDF_AttributeArray1", "TDF_AttributeDataMap", "TDF_AttributeDelta", "TDF_AttributeDeltaList", "TDF_AttributeDoubleMap", "TDF_AttributeIndexedMap", "TDF_AttributeIterator", "TDF_AttributeList", "TDF_AttributeMap", "TDF_AttributeSequence", "TDF_ChildIDIterator", "TDF_ChildIterator", "TDF_ClosureMode", "TDF_ClosureTool", "TDF_ComparisonTool", "TDF_CopyLabel", "TDF_CopyTool", "TDF_Data", "TDF_DataSet", "TDF_DefaultDeltaOnModification", "TDF_DefaultDeltaOnRemoval", "TDF_Delta", "TDF_DeltaList", "TDF_DeltaOnAddition", "TDF_DeltaOnForget", "TDF_DeltaOnModification", "TDF_DeltaOnRemoval", "TDF_DeltaOnResume", "TDF_GUIDProgIDMap", "TDF_HAllocator", "TDF_HAttributeArray1", "TDF_IDFilter", "TDF_IDList", "TDF_IDMap", "TDF_Label", "TDF_LabelDataMap", "TDF_LabelDoubleMap", "TDF_LabelIndexedMap", "TDF_LabelIntegerMap", "TDF_LabelList", "TDF_LabelMap", "TDF_LabelSequence", "TDF_ListIteratorOfAttributeDeltaList", "TDF_ListIteratorOfAttributeList", "TDF_ListIteratorOfDeltaList", "TDF_ListIteratorOfIDList", "TDF_ListIteratorOfLabelList", "TDF_Reference", "TDF_RelocationTable", "TDF_TagSource", "TDF_Tool", "TDF_Transaction", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tdf"], "children": []}, {"name": "OCC.Core.TDataStd", "type": "module", "classes": ["SwigPyIterator", "TDataStd_AsciiString", "TDataStd_BooleanArray", "TDataStd_BooleanList", "TDataStd_ByteArray", "TDataStd_ChildNodeIterator", "TDataStd_Comment", "TDataStd_Current", "TDataStd_DataMapOfStringByte", "TDataStd_DataMapOfStringHArray1OfInteger", "TDataStd_DataMapOfStringHArray1OfReal", "TDataStd_DataMapOfStringReal", "TDataStd_DataMapOfStringString", "TDataStd_DeltaOnModificationOfByteArray", "TDataStd_DeltaOnModificationOfExtStringArray", "TDataStd_DeltaOnModificationOfIntArray", "TDataStd_DeltaOnModificationOfIntPackedMap", "TDataStd_DeltaOnModificationOfRealArray", "TDataStd_Directory", "TDataStd_Expression", "TDataStd_ExtStringArray", "TDataStd_ExtStringList", "TDataStd_HDataMapOfStringByte", "TDataStd_HDataMapOfStringHArray1OfInteger", "TDataStd_HDataMapOfStringHArray1OfReal", "TDataStd_HDataMapOfStringInteger", "TDataStd_HDataMapOfStringReal", "TDataStd_HDataMapOfStringString", "TDataStd_HLabelArray1", "TDataStd_IntPackedMap", "TDataStd_Integer", "TDataStd_IntegerArray", "TDataStd_IntegerList", "TDataStd_LabelArray1", "TDataStd_ListIteratorOfListOfByte", "TDataStd_ListIteratorOfListOfExtendedString", "TDataStd_ListOfByte", "TDataStd_ListOfExtendedString", "TDataStd_Name", "TDataStd_NamedData", "TDataStd_NoteBook", "TDataStd_Real", "TDataStd_RealArray", "TDataStd_RealEnum", "TDataStd_RealList", "TDataStd_ReferenceArray", "TDataStd_ReferenceList", "TDataStd_Relation", "TDataStd_Tick", "TDataStd_TreeNode", "TDataStd_UAttribute", "TDataStd_Variable", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tdatastd"], "children": []}, {"name": "OCC.Core.TDataXtd", "type": "module", "classes": ["SwigPyIterator", "TDataXtd_Array1OfTrsf", "TDataXtd_Axis", "TDataXtd_Constraint", "TDataXtd_ConstraintEnum", "TDataXtd_Geometry", "TDataXtd_GeometryEnum", "TDataXtd_HArray1OfTrsf", "TDataXtd_Pattern", "TDataXtd_PatternStd", "TDataXtd_Placement", "TDataXtd_Plane", "TDataXtd_Point", "TDataXtd_Position", "TDataXtd_Presentation", "TDataXtd_Shape", "TDataXtd_Triangulation", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tdataxtd"], "children": []}, {"name": "OCC.Core.TDocStd", "type": "module", "classes": ["SwigPyIterator", "TDocStd_Application", "TDocStd_ApplicationDelta", "TDocStd_CompoundDelta", "TDocStd_Context", "TDocStd_Document", "TDocStd_FormatVersion", "TDocStd_LabelIDMapDataMap", "TDocStd_Modified", "TDocStd_MultiTransactionManager", "TDocStd_Owner", "TDocStd_PathParser", "TDocStd_SequenceOfApplicationDelta", "TDocStd_SequenceOfDocument", "TDocStd_XLink", "TDocStd_XLinkIterator", "TDocStd_XLinkRoot", "TDocStd_XLinkTool", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tdocstd"], "children": []}, {"name": "OCC.Core.TFunction", "type": "module", "classes": ["SwigPyIterator", "TFunction_DataMapOfGUIDDriver", "TFunction_DataMapOfLabelListOfLabel", "TFunction_DoubleMapOfIntegerLabel", "TFunction_Driver", "TFunction_DriverTable", "TFunction_ExecutionStatus", "TFunction_Function", "TFunction_GraphNode", "TFunction_HArray1OfDataMapOfGUIDDriver", "TFunction_IFunction", "TFunction_Iterator", "TFunction_Logbook", "TFunction_Scope", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TNaming", "type": "module", "classes": ["SwigPyIterator", "TNaming_Builder", "TNaming_CopyShape", "TNaming_DataMapOfShapeMapOfShape", "TNaming_DataMapOfShapePtrRefShape", "TNaming_DataMapOfShapeShapesSet", "TNaming_DeltaOnModification", "TNaming_DeltaOnRemoval", "TNaming_Evolution", "TNaming_Identifier", "TNaming_Iterator", "TNaming_IteratorOnShapesSet", "TNaming_ListIteratorOfListOfIndexedDataMapOfShapeListOfShape", "TNaming_ListIteratorOfListOfMapOfShape", "TNaming_ListIteratorOfListOfNamedShape", "TNaming_ListOfIndexedDataMapOfShapeListOfShape", "TNaming_ListOfMapOfShape", "TNaming_ListOfNamedShape", "TNaming_Localizer", "TNaming_MapOfNamedShape", "TNaming_Name", "TNaming_NameType", "TNaming_NamedShape", "TNaming_Naming", "TNaming_NamingTool", "TNaming_NewShapeIterator", "TNaming_OldShapeIterator", "TNaming_RefShape", "TNaming_SameShapeIterator", "TNaming_Scope", "TNaming_Selector", "TNaming_ShapesSet", "TNaming_Tool", "TNaming_TranslateTool", "TNaming_Translator", "TNaming_UsedShapes", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "tnaming"], "children": []}, {"name": "OCC.Core.TObj", "type": "module", "classes": ["SwigPyIterator", "TObj_Application", "TObj_Assistant", "TObj_CheckModel", "TObj_DataMapOfNameLabel", "TObj_DataMapOfObjectHSequenceOcafObjects", "TObj_DataMapOfStringPointer", "TObj_DeletingMode", "TObj_HSequenceOfObject", "TObj_HiddenPartition", "TObj_LabelIterator", "TObj_Model", "TObj_ModelIterator", "TObj_Object", "TObj_ObjectIterator", "TObj_OcafObjectIterator", "TObj_Partition", "TObj_Persistence", "TObj_ReferenceIterator", "TObj_SequenceIterator", "TObj_SequenceOfIterator", "TObj_SequenceOfObject", "TObj_TIntSparseArray", "TObj_TIntSparseArray_MapOfData", "TObj_TModel", "TObj_TNameContainer", "TObj_TObject", "TObj_TReference", "TObj_TXYZ", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TPrsStd", "type": "module", "classes": ["SwigPyIterator", "TPrsStd_AISPresentation", "TPrsStd_AISViewer", "TPrsStd_AxisDriver", "TPrsStd_ConstraintDriver", "TPrsStd_ConstraintTools", "TPrsStd_DataMapOfGUIDDriver", "TPrsStd_Driver", "TPrsStd_DriverTable", "TPrsStd_GeometryDriver", "TPrsStd_NamedShapeDriver", "TPrsStd_PlaneDriver", "TPrsStd_PointDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TShort", "type": "module", "classes": ["SwigPyIterator", "TShort_Array1OfShortReal", "TShort_Array2OfShortReal", "TShort_HArray1OfShortReal", "TShort_HArray2OfShortReal", "TShort_HSequenceOfShortReal", "TShort_SequenceOfShortReal", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.Tesselator", "type": "module", "classes": ["ShapeTesselator", "SwigPyIterator", "_SwigNonDynamicMeta", "vector_float"], "children": []}, {"name": "OCC.Core.TopAbs", "type": "module", "classes": ["SwigPyIterator", "TopAbs_Orientation", "TopAbs_ShapeEnum", "TopAbs_State", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topabs"], "children": []}, {"name": "OCC.Core.TopBas", "type": "module", "classes": ["SwigPyIterator", "TopBas_ListIteratorOfListOfTestInterference", "TopBas_ListOfTestInterference", "TopBas_TestInterference", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopClass", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopCnx", "type": "module", "classes": ["SwigPyIterator", "TopCnx_EdgeFaceTransition", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopExp", "type": "module", "classes": ["SwigPyIterator", "TopExp_Explorer", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topexp"], "children": []}, {"name": "OCC.Core.TopLoc", "type": "module", "classes": ["SwigPyIterator", "TopLoc_Datum3D", "TopLoc_IndexedMapOfLocation", "TopLoc_ItemLocation", "TopLoc_Location", "TopLoc_MapOfLocation", "TopLoc_SListNodeOfItemLocation", "TopLoc_SListOfItemLocation", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopOpeBRep", "type": "module", "classes": ["SwigPyIterator", "TopOpeBRep_Array1OfLineInter", "TopOpeBRep_Array1OfVPointInter", "TopOpeBRep_Bipoint", "TopOpeBRep_DSFiller", "TopOpeBRep_DataMapOfTopolTool", "TopOpeBRep_EdgesFiller", "TopOpeBRep_EdgesIntersector", "TopOpeBRep_FFDumper", "TopOpeBRep_FFTransitionTool", "TopOpeBRep_FaceEdgeFiller", "TopOpeBRep_FaceEdgeIntersector", "TopOpeBRep_FacesFiller", "TopOpeBRep_FacesIntersector", "TopOpeBRep_GeomTool", "TopOpeBRep_HArray1OfLineInter", "TopOpeBRep_HArray1OfVPointInter", "TopOpeBRep_Hctxee2d", "TopOpeBRep_Hctxff2d", "TopOpeBRep_LineInter", "TopOpeBRep_ListIteratorOfListOfBipoint", "TopOpeBRep_ListOfBipoint", "TopOpeBRep_P2Dstatus", "TopOpeBRep_Point2d", "TopOpeBRep_PointClassifier", "TopOpeBRep_PointGeomTool", "TopOpeBRep_SequenceOfPoint2d", "TopOpeBRep_ShapeIntersector", "TopOpeBRep_ShapeIntersector2d", "TopOpeBRep_ShapeScanner", "TopOpeBRep_TypeLineCurve", "TopOpeBRep_VPointInter", "TopOpeBRep_VPointInterClassifier", "TopOpeBRep_VPointInterIterator", "TopOpeBRep_WPointInter", "TopOpeBRep_WPointInterIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topopebrep"], "children": []}, {"name": "OCC.Core.TopOpeBRepBuild", "type": "module", "classes": ["SwigPyIterator", "TopOpeBRepBuild_Area1dBuilder", "TopOpeBRepBuild_Area2dBuilder", "TopOpeBRepBuild_Area3dBuilder", "TopOpeBRepBuild_AreaBuilder", "TopOpeBRepBuild_BlockBuilder", "TopOpeBRepBuild_BlockIterator", "TopOpeBRepBuild_Builder", "TopOpeBRepBuild_Builder1", "TopOpeBRepBuild_BuilderON", "TopOpeBRepBuild_CompositeClassifier", "TopOpeBRepBuild_CorrectFace2d", "TopOpeBRepBuild_DataMapOfShapeListOfShapeListOfShape", "TopOpeBRepBuild_EdgeBuilder", "TopOpeBRepBuild_FaceAreaBuilder", "TopOpeBRepBuild_FaceBuilder", "TopOpeBRepBuild_FuseFace", "TopOpeBRepBuild_GIter", "TopOpeBRepBuild_GTool", "TopOpeBRepBuild_GTopo", "TopOpeBRepBuild_HBuilder", "TopOpeBRepBuild_IndexedDataMapOfShapeVertexInfo", "TopOpeBRepBuild_ListIteratorOfListOfListOfLoop", "TopOpeBRepBuild_ListIteratorOfListOfLoop", "TopOpeBRepBuild_ListIteratorOfListOfPave", "TopOpeBRepBuild_ListIteratorOfListOfShapeListOfShape", "TopOpeBRepBuild_ListOfListOfLoop", "TopOpeBRepBuild_ListOfLoop", "TopOpeBRepBuild_ListOfPave", "TopOpeBRepBuild_ListOfShapeListOfShape", "TopOpeBRepBuild_Loop", "TopOpeBRepBuild_LoopClassifier", "TopOpeBRepBuild_LoopEnum", "TopOpeBRepBuild_LoopSet", "TopOpeBRepBuild_Pave", "TopOpeBRepBuild_PaveClassifier", "TopOpeBRepBuild_PaveSet", "TopOpeBRepBuild_ShapeListOfShape", "TopOpeBRepBuild_ShapeSet", "TopOpeBRepBuild_ShellFaceClassifier", "TopOpeBRepBuild_ShellFaceSet", "TopOpeBRepBuild_ShellToSolid", "TopOpeBRepBuild_SolidAreaBuilder", "TopOpeBRepBuild_SolidBuilder", "TopOpeBRepBuild_Tools", "TopOpeBRepBuild_Tools2d", "TopOpeBRepBuild_VertexInfo", "TopOpeBRepBuild_WireEdgeClassifier", "TopOpeBRepBuild_WireEdgeSet", "TopOpeBRepBuild_WireToFace", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopOpeBRepDS", "type": "module", "classes": ["SwigPyIterator", "TopOpeBRepDS_Association", "TopOpeBRepDS_BuildTool", "TopOpeBRepDS_Check", "TopOpeBRepDS_CheckStatus", "TopOpeBRepDS_Config", "TopOpeBRepDS_Curve", "TopOpeBRepDS_CurveData", "TopOpeBRepDS_CurveExplorer", "TopOpeBRepDS_CurveIterator", "TopOpeBRepDS_CurvePointInterference", "TopOpeBRepDS_DataMapOfCheckStatus", "TopOpeBRepDS_DataMapOfIntegerListOfInterference", "TopOpeBRepDS_DataMapOfInterferenceListOfInterference", "TopOpeBRepDS_DataMapOfInterferenceShape", "TopOpeBRepDS_DataMapOfShapeListOfShapeOn1State", "TopOpeBRepDS_DataMapOfShapeState", "TopOpeBRepDS_DataStructure", "TopOpeBRepDS_DoubleMapOfIntegerShape", "TopOpeBRepDS_Dumper", "TopOpeBRepDS_EIR", "TopOpeBRepDS_Edge3dInterferenceTool", "TopOpeBRepDS_EdgeInterferenceTool", "TopOpeBRepDS_EdgeVertexInterference", "TopOpeBRepDS_Explorer", "TopOpeBRepDS_FIR", "TopOpeBRepDS_FaceEdgeInterference", "TopOpeBRepDS_FaceInterferenceTool", "TopOpeBRepDS_Filter", "TopOpeBRepDS_GapFiller", "TopOpeBRepDS_GapTool", "TopOpeBRepDS_GeometryData", "TopOpeBRepDS_HArray1OfDataMapOfIntegerListOfInterference", "TopOpeBRepDS_IndexedDataMapOfShapeWithState", "TopOpeBRepDS_IndexedDataMapOfVertexPoint", "TopOpeBRepDS_Interference", "TopOpeBRepDS_InterferenceIterator", "TopOpeBRepDS_InterferenceTool", "TopOpeBRepDS_Kind", "TopOpeBRepDS_ListIteratorOfListOfInterference", "TopOpeBRepDS_ListOfInterference", "TopOpeBRepDS_ListOfShapeOn1State", "TopOpeBRepDS_MapOfCurve", "TopOpeBRepDS_MapOfIntegerShapeData", "TopOpeBRepDS_MapOfPoint", "TopOpeBRepDS_MapOfShapeData", "TopOpeBRepDS_MapOfSurface", "TopOpeBRepDS_Marker", "TopOpeBRepDS_Point", "TopOpeBRepDS_PointData", "TopOpeBRepDS_PointExplorer", "TopOpeBRepDS_PointIterator", "TopOpeBRepDS_Reducer", "TopOpeBRepDS_ShapeData", "TopOpeBRepDS_ShapeShapeInterference", "TopOpeBRepDS_ShapeSurface", "TopOpeBRepDS_ShapeWithState", "TopOpeBRepDS_SolidSurfaceInterference", "TopOpeBRepDS_Surface", "TopOpeBRepDS_SurfaceCurveInterference", "TopOpeBRepDS_SurfaceData", "TopOpeBRepDS_SurfaceExplorer", "TopOpeBRepDS_SurfaceIterator", "TopOpeBRepDS_TKI", "TopOpeBRepDS_TOOL", "TopOpeBRepDS_Transition", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topopebrepds"], "children": []}, {"name": "OCC.Core.TopOpeBRepTool", "type": "module", "classes": ["SwigPyIterator", "TopOpeBRepTool_AncestorsTool", "TopOpeBRepTool_BoxSort", "TopOpeBRepTool_C2DF", "TopOpeBRepTool_CLASSI", "TopOpeBRepTool_CORRISO", "TopOpeBRepTool_CurveTool", "TopOpeBRepTool_DataMapOfOrientedShapeC2DF", "TopOpeBRepTool_DataMapOfShapeListOfC2DF", "TopOpeBRepTool_DataMapOfShapeface", "TopOpeBRepTool_FuseEdges", "TopOpeBRepTool_GeomTool", "TopOpeBRepTool_HBoxTool", "TopOpeBRepTool_IndexedDataMapOfShapeBox", "TopOpeBRepTool_IndexedDataMapOfShapeBox2d", "TopOpeBRepTool_IndexedDataMapOfShapeconnexity", "TopOpeBRepTool_ListIteratorOfListOfC2DF", "TopOpeBRepTool_ListOfC2DF", "TopOpeBRepTool_OutCurveType", "TopOpeBRepTool_PurgeInternalEdges", "TopOpeBRepTool_REGUS", "TopOpeBRepTool_REGUW", "TopOpeBRepTool_ShapeClassifier", "TopOpeBRepTool_ShapeExplorer", "TopOpeBRepTool_ShapeTool", "TopOpeBRepTool_SolidClassifier", "TopOpeBRepTool_TOOL", "TopOpeBRepTool_connexity", "TopOpeBRepTool_face", "TopOpeBRepTool_makeTransition", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topopebreptool"], "children": []}, {"name": "OCC.Core.TopTools", "type": "module", "classes": ["SwigPyIterator", "TopTools_Array1OfShape", "TopTools_Array2OfShape", "TopTools_DataMapOfIntegerListOfShape", "TopTools_DataMapOfIntegerShape", "TopTools_DataMapOfOrientedShapeInteger", "TopTools_DataMapOfOrientedShapeShape", "TopTools_DataMapOfShapeBox", "TopTools_DataMapOfShapeInteger", "TopTools_DataMapOfShapeListOfInteger", "TopTools_DataMapOfShapeListOfShape", "TopTools_DataMapOfShapeReal", "TopTools_DataMapOfShapeSequenceOfShape", "TopTools_DataMapOfShapeShape", "TopTools_FormatVersion", "TopTools_HArray1OfListOfShape", "TopTools_HArray1OfShape", "TopTools_HArray2OfShape", "TopTools_HSequenceOfShape", "TopTools_IndexedDataMapOfShapeAddress", "TopTools_IndexedDataMapOfShapeListOfShape", "TopTools_IndexedDataMapOfShapeReal", "TopTools_IndexedDataMapOfShapeShape", "TopTools_IndexedMapOfOrientedShape", "TopTools_IndexedMapOfShape", "TopTools_ListIteratorOfListOfShape", "TopTools_ListOfListOfShape", "TopTools_ListOfShape", "TopTools_LocationSet", "TopTools_MapOfOrientedShape", "TopTools_MapOfShape", "TopTools_MutexForShapeProvider", "TopTools_SequenceOfShape", "TopTools_ShapeMapHasher", "TopTools_ShapeSet", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "toptools"], "children": []}, {"name": "OCC.Core.TopTrans", "type": "module", "classes": ["SwigPyIterator", "TopTrans_CurveTransition", "TopTrans_SurfaceTransition", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TopoDS", "type": "module", "classes": ["SwigPyIterator", "TopoDS_AlertAttribute", "TopoDS_AlertWithShape", "TopoDS_Builder", "TopoDS_CompSolid", "TopoDS_Compound", "TopoDS_Edge", "TopoDS_Face", "TopoDS_HShape", "TopoDS_Iterator", "TopoDS_Shape", "TopoDS_Shell", "TopoDS_Solid", "TopoDS_TCompSolid", "TopoDS_TCompound", "TopoDS_TEdge", "TopoDS_TFace", "TopoDS_TShape", "TopoDS_TShell", "TopoDS_TSolid", "TopoDS_TVertex", "TopoDS_TWire", "TopoDS_Vertex", "TopoDS_Wire", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topods"], "children": []}, {"name": "OCC.Core.TopoDSToStep", "type": "module", "classes": ["SwigPyIterator", "TopoDSToStep_Builder", "TopoDSToStep_BuilderError", "TopoDSToStep_FacetedError", "TopoDSToStep_FacetedTool", "TopoDSToStep_MakeBrepWithVoids", "TopoDSToStep_MakeEdgeError", "TopoDSToStep_MakeFaceError", "TopoDSToStep_MakeFacetedBrep", "TopoDSToStep_MakeFacetedBrepAndBrepWithVoids", "TopoDSToStep_MakeGeometricCurveSet", "TopoDSToStep_MakeManifoldSolidBrep", "TopoDSToStep_MakeShellBasedSurfaceModel", "TopoDSToStep_MakeStepEdge", "TopoDSToStep_MakeStepFace", "TopoDSToStep_MakeStepVertex", "TopoDSToStep_MakeStepWire", "TopoDSToStep_MakeTessellatedItem", "TopoDSToStep_MakeVertexError", "TopoDSToStep_MakeWireError", "TopoDSToStep_Root", "TopoDSToStep_Tool", "TopoDSToStep_WireframeBuilder", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "topodstostep"], "children": []}, {"name": "OCC.Core.Transfer", "type": "module", "classes": ["SwigPyIterator", "Transfer_ActorDispatch", "Transfer_ActorOfFinderProcess", "Transfer_ActorOfProcessForFinder", "Transfer_ActorOfProcessForTransient", "Transfer_ActorOfTransientProcess", "Transfer_Binder", "Transfer_BinderOfTransientInteger", "Transfer_DataInfo", "Transfer_DispatchControl", "Transfer_FindHasher", "Transfer_Finder", "Transfer_FinderProcess", "Transfer_HSequenceOfBinder", "Transfer_HSequenceOfFinder", "Transfer_IteratorOfProcessForFinder", "Transfer_IteratorOfProcessForTransient", "Transfer_MapContainer", "Transfer_MultipleBinder", "Transfer_ProcessForTransient", "Transfer_ResultFromModel", "Transfer_ResultFromTransient", "Transfer_SequenceOfBinder", "Transfer_SequenceOfFinder", "Transfer_SimpleBinderOfTransient", "Transfer_StatusExec", "Transfer_StatusResult", "Transfer_TransferDispatch", "Transfer_TransferInput", "Transfer_TransferIterator", "Transfer_TransferMapOfProcessForFinder", "Transfer_TransferMapOfProcessForTransient", "Transfer_TransferOutput", "Transfer_TransientListBinder", "Transfer_TransientMapper", "Transfer_TransientProcess", "Transfer_UndefMode", "Transfer_VoidBinder", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.TransferBRep", "type": "module", "classes": ["SwigPyIterator", "TransferBRep_BinderOfShape", "TransferBRep_HSequenceOfTransferResultInfo", "TransferBRep_Reader", "TransferBRep_SequenceOfTransferResultInfo", "TransferBRep_ShapeBinder", "TransferBRep_ShapeInfo", "TransferBRep_ShapeListBinder", "TransferBRep_ShapeMapper", "TransferBRep_TransferResultInfo", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "transferbrep"], "children": []}, {"name": "OCC.Core.UTL", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "utl"], "children": []}, {"name": "OCC.Core.Units", "type": "module", "classes": ["SwigPyIterator", "Units_Dimensions", "Units_Explorer", "Units_Lexicon", "Units_MathSentence", "Units_Measurement", "Units_QtsSequence", "Units_QuantitiesSequence", "Units_Quantity", "Units_Sentence", "Units_ShiftedToken", "Units_ShiftedUnit", "Units_TksSequence", "Units_Token", "Units_TokensSequence", "Units_Unit", "Units_UnitSentence", "Units_UnitsDictionary", "Units_UnitsLexicon", "Units_UnitsSequence", "Units_UnitsSystem", "Units_UtsSequence", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "units"], "children": []}, {"name": "OCC.Core.UnitsAPI", "type": "module", "classes": ["SwigPyIterator", "UnitsAPI_SystemUnits", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "unitsapi"], "children": []}, {"name": "OCC.Core.UnitsMethods", "type": "module", "classes": ["SwigPyIterator", "UnitsMethods_LengthUnit", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "unitsmethods"], "children": []}, {"name": "OCC.Core.V3d", "type": "module", "classes": ["SwigPyIterator", "V3d_AmbientLight", "V3d_CircularGrid", "V3d_DirectionalLight", "V3d_ListOfLight", "V3d_ListOfView", "V3d_Plane", "V3d_PositionLight", "V3d_PositionalLight", "V3d_RectangularGrid", "V3d_SpotLight", "V3d_StereoDumpOptions", "V3d_Trihedron", "V3d_TypeOfAxe", "V3d_TypeOfOrientation", "V3d_TypeOfView", "V3d_TypeOfVisualization", "V3d_View", "V3d_Viewer", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "v3d"], "children": []}, {"name": "OCC.Core.Visualization", "type": "module", "classes": ["Display3d", "SwigPyIterator", "_SwigNonDynamicMeta"], "children": []}, {"name": "OCC.Core.Vrml", "type": "module", "classes": ["SwigPyIterator", "Vrml_AsciiText", "Vrml_AsciiTextJustification", "Vrml_Cone", "Vrml_ConeParts", "Vrml_Coordinate3", "Vrml_Cube", "Vrml_Cylinder", "Vrml_CylinderParts", "Vrml_DirectionalLight", "Vrml_FaceType", "Vrml_FontStyle", "Vrml_FontStyleFamily", "Vrml_FontStyleStyle", "Vrml_Group", "Vrml_IndexedFaceSet", "Vrml_IndexedLineSet", "Vrml_Info", "Vrml_Instancing", "Vrml_LOD", "Vrml_Material", "Vrml_MaterialBinding", "Vrml_MaterialBindingAndNormalBinding", "Vrml_MatrixTransform", "Vrml_Normal", "Vrml_NormalBinding", "Vrml_OrthographicCamera", "Vrml_PerspectiveCamera", "Vrml_PointLight", "Vrml_PointSet", "Vrml_Rotation", "Vrml_SFImage", "Vrml_SFImageNumber", "Vrml_SFRotation", "Vrml_Scale", "Vrml_Separator", "Vrml_SeparatorRenderCulling", "Vrml_ShapeHints", "Vrml_ShapeType", "Vrml_Sphere", "Vrml_SpotLight", "Vrml_Switch", "Vrml_Texture2", "Vrml_Texture2Transform", "Vrml_Texture2Wrap", "Vrml_TextureCoordinate2", "Vrml_Transform", "Vrml_TransformSeparator", "Vrml_Translation", "Vrml_VertexOrdering", "Vrml_WWWAnchor", "Vrml_WWWAnchorMap", "Vrml_WWWInline", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "vrml"], "children": []}, {"name": "OCC.Core.VrmlAPI", "type": "module", "classes": ["SwigPyIterator", "VrmlAPI_CafReader", "VrmlAPI_RepresentationOfShape", "VrmlAPI_Writer", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "vrmlapi"], "children": []}, {"name": "OCC.Core.VrmlConverter", "type": "module", "classes": ["SwigPyIterator", "VrmlConverter_Curve", "VrmlConverter_DeflectionCurve", "VrmlConverter_Drawer", "VrmlConverter_HLRShape", "VrmlConverter_IsoAspect", "VrmlConverter_LineAspect", "VrmlConverter_PointAspect", "VrmlConverter_Projector", "VrmlConverter_ShadedShape", "VrmlConverter_ShadingAspect", "VrmlConverter_TypeOfCamera", "VrmlConverter_TypeOfLight", "VrmlConverter_WFDeflectionRestrictedFace", "VrmlConverter_WFDeflectionShape", "VrmlConverter_WFRestrictedFace", "VrmlConverter_WFShape", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.VrmlData", "type": "module", "classes": ["SwigPyIterator", "VrmlData_Appearance", "VrmlData_Box", "VrmlData_Color", "VrmlData_Cone", "VrmlData_Coordinate", "VrmlData_Cylinder", "VrmlData_DataMapOfShapeAppearance", "VrmlData_ErrorStatus", "VrmlData_Geometry", "VrmlData_Group", "VrmlData_ImageTexture", "VrmlData_IndexedFaceSet", "VrmlData_IndexedLineSet", "VrmlData_ListOfNode", "VrmlData_MapOfNode", "VrmlData_Material", "VrmlData_Node", "VrmlData_Normal", "VrmlData_Scene", "VrmlData_ShapeConvert", "VrmlData_ShapeNode", "VrmlData_Sphere", "VrmlData_TextureCoordinate", "VrmlData_UnknownNode", "VrmlData_WorldInfo", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.XBRepMesh", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xbrepmesh"], "children": []}, {"name": "OCC.Core.XCAFApp", "type": "module", "classes": ["SwigPyIterator", "XCAFApp_Application", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.XCAFDimTolObjects", "type": "module", "classes": ["SwigPyIterator", "XCAFDimTolObjects_AngularQualifier", "XCAFDimTolObjects_DataMapOfToleranceDatum", "XCAFDimTolObjects_DatumModifWithValue", "XCAFDimTolObjects_DatumModifiersSequence", "XCAFDimTolObjects_DatumObject", "XCAFDimTolObjects_DatumObjectSequence", "XCAFDimTolObjects_DatumSingleModif", "XCAFDimTolObjects_DatumTargetType", "XCAFDimTolObjects_DimensionFormVariance", "XCAFDimTolObjects_DimensionGrade", "XCAFDimTolObjects_DimensionModif", "XCAFDimTolObjects_DimensionModifiersSequence", "XCAFDimTolObjects_DimensionObject", "XCAFDimTolObjects_DimensionObjectSequence", "XCAFDimTolObjects_DimensionQualifier", "XCAFDimTolObjects_DimensionType", "XCAFDimTolObjects_GeomToleranceMatReqModif", "XCAFDimTolObjects_GeomToleranceModif", "XCAFDimTolObjects_GeomToleranceModifiersSequence", "XCAFDimTolObjects_GeomToleranceObject", "XCAFDimTolObjects_GeomToleranceObjectSequence", "XCAFDimTolObjects_GeomToleranceType", "XCAFDimTolObjects_GeomToleranceTypeValue", "XCAFDimTolObjects_GeomToleranceZoneModif", "XCAFDimTolObjects_ToleranceZoneAffectedPlane", "XCAFDimTolObjects_Tool", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.XCAFDoc", "type": "module", "classes": ["SwigPyIterator", "XCAFDoc_Area", "XCAFDoc_AssemblyGraph", "XCAFDoc_AssemblyItemId", "XCAFDoc_AssemblyItemRef", "XCAFDoc_AssemblyIterator", "XCAFDoc_AssemblyTool", "XCAFDoc_Centroid", "XCAFDoc_ClippingPlaneTool", "XCAFDoc_Color", "XCAFDoc_ColorTool", "XCAFDoc_ColorType", "XCAFDoc_DataMapOfShapeLabel", "XCAFDoc_Datum", "XCAFDoc_DimTol", "XCAFDoc_DimTolTool", "XCAFDoc_Dimension", "XCAFDoc_DocumentTool", "XCAFDoc_Editor", "XCAFDoc_GraphNode", "XCAFDoc_GraphNodeSequence", "XCAFDoc_LayerTool", "XCAFDoc_LengthUnit", "XCAFDoc_Location", "XCAFDoc_Material", "XCAFDoc_MaterialTool", "XCAFDoc_Note", "XCAFDoc_NoteBalloon", "XCAFDoc_NoteBinData", "XCAFDoc_NoteComment", "XCAFDoc_NotesTool", "XCAFDoc_ShapeMapTool", "XCAFDoc_ShapeTool", "XCAFDoc_View", "XCAFDoc_ViewTool", "XCAFDoc_VisMaterial", "XCAFDoc_VisMaterialCommon", "XCAFDoc_VisMaterialPBR", "XCAFDoc_VisMaterialTool", "XCAFDoc_Volume", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xcafdoc"], "children": []}, {"name": "OCC.Core.XCAFNoteObjects", "type": "module", "classes": ["SwigPyIterator", "XCAFNoteObjects_NoteObject", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.XCAFPrs", "type": "module", "classes": ["SwigPyIterator", "XCAFPrs_AISObject", "XCAFPrs_DataMapOfStyleShape", "XCAFPrs_DataMapOfStyleTransient", "XCAFPrs_DocumentExplorer", "XCAFPrs_DocumentIdIterator", "XCAFPrs_DocumentNode", "XCAFPrs_Driver", "XCAFPrs_IndexedDataMapOfShapeStyle", "XCAFPrs_Style", "XCAFPrs_Texture", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xcafprs"], "children": []}, {"name": "OCC.Core.XCAFView", "type": "module", "classes": ["SwigPyIterator", "XCAFView_Object", "XCAFView_ProjectionType", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.XSControl", "type": "module", "classes": ["SwigPyIterator", "XSControl_ConnectedShapes", "XSControl_Controller", "XSControl_FuncShape", "XSControl_Functions", "XSControl_Reader", "XSControl_SelectForTransfer", "XSControl_SignTransferStatus", "XSControl_TransferReader", "XSControl_TransferWriter", "XSControl_Utils", "XSControl_Vars", "XSControl_WorkSession", "XSControl_Writer", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xscontrol"], "children": []}, {"name": "OCC.Core.XmlDrivers", "type": "module", "classes": ["SwigPyIterator", "XmlDrivers_DocumentRetrievalDriver", "XmlDrivers_DocumentStorageDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmldrivers"], "children": []}, {"name": "OCC.Core.XmlLDrivers", "type": "module", "classes": ["SwigPyIterator", "XmlLDrivers_DocumentRetrievalDriver", "XmlLDrivers_DocumentStorageDriver", "XmlLDrivers_NamespaceDef", "XmlLDrivers_SequenceOfNamespaceDef", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlldrivers"], "children": []}, {"name": "OCC.Core.XmlMDF", "type": "module", "classes": ["SwigPyIterator", "XmlMDF_ADriver", "XmlMDF_ADriverTable", "XmlMDF_DerivedDriver", "XmlMDF_MapOfDriver", "XmlMDF_ReferenceDriver", "XmlMDF_TagSourceDriver", "XmlMDF_TypeADriverMap", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmdf"], "children": []}, {"name": "OCC.Core.XmlMDataStd", "type": "module", "classes": ["SwigPyIterator", "XmlMDataStd_AsciiStringDriver", "XmlMDataStd_BooleanArrayDriver", "XmlMDataStd_BooleanListDriver", "XmlMDataStd_ByteArrayDriver", "XmlMDataStd_ExpressionDriver", "XmlMDataStd_ExtStringArrayDriver", "XmlMDataStd_ExtStringListDriver", "XmlMDataStd_GenericEmptyDriver", "XmlMDataStd_GenericExtStringDriver", "XmlMDataStd_IntPackedMapDriver", "XmlMDataStd_IntegerArrayDriver", "XmlMDataStd_IntegerDriver", "XmlMDataStd_IntegerListDriver", "XmlMDataStd_NamedDataDriver", "XmlMDataStd_RealArrayDriver", "XmlMDataStd_RealDriver", "XmlMDataStd_RealListDriver", "XmlMDataStd_ReferenceArrayDriver", "XmlMDataStd_ReferenceListDriver", "XmlMDataStd_TreeNodeDriver", "XmlMDataStd_UAttributeDriver", "XmlMDataStd_VariableDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmdatastd"], "children": []}, {"name": "OCC.Core.XmlMDataXtd", "type": "module", "classes": ["SwigPyIterator", "XmlMDataXtd_ConstraintDriver", "XmlMDataXtd_GeometryDriver", "XmlMDataXtd_PatternStdDriver", "XmlMDataXtd_PositionDriver", "XmlMDataXtd_PresentationDriver", "XmlMDataXtd_TriangulationDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmdataxtd"], "children": []}, {"name": "OCC.Core.XmlMDocStd", "type": "module", "classes": ["SwigPyIterator", "XmlMDocStd_XLinkDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmdocstd"], "children": []}, {"name": "OCC.Core.XmlMFunction", "type": "module", "classes": ["SwigPyIterator", "XmlMFunction_FunctionDriver", "XmlMFunction_GraphNodeDriver", "XmlMFunction_ScopeDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmfunction"], "children": []}, {"name": "OCC.Core.XmlMNaming", "type": "module", "classes": ["SwigPyIterator", "XmlMNaming_NamedShapeDriver", "XmlMNaming_NamingDriver", "XmlMNaming_Shape1", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmnaming"], "children": []}, {"name": "OCC.Core.XmlMXCAFDoc", "type": "module", "classes": ["SwigPyIterator", "XmlMXCAFDoc_AssemblyItemRefDriver", "XmlMXCAFDoc_CentroidDriver", "XmlMXCAFDoc_ColorDriver", "XmlMXCAFDoc_DatumDriver", "XmlMXCAFDoc_DimTolDriver", "XmlMXCAFDoc_GraphNodeDriver", "XmlMXCAFDoc_LengthUnitDriver", "XmlMXCAFDoc_LocationDriver", "XmlMXCAFDoc_MaterialDriver", "XmlMXCAFDoc_NoteBinDataDriver", "XmlMXCAFDoc_NoteCommentDriver", "XmlMXCAFDoc_NoteDriver", "XmlMXCAFDoc_VisMaterialDriver", "XmlMXCAFDoc_VisMaterialToolDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlmxcafdoc"], "children": []}, {"name": "OCC.Core.XmlObjMgt", "type": "module", "classes": ["SwigPyIterator", "XmlObjMgt_Array1", "XmlObjMgt_GP", "XmlObjMgt_Persistent", "XmlObjMgt_RRelocationTable", "XmlObjMgt_SRelocationTable", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlobjmgt"], "children": []}, {"name": "OCC.Core.XmlTObjDrivers", "type": "module", "classes": ["SwigPyIterator", "XmlTObjDrivers_DocumentRetrievalDriver", "XmlTObjDrivers_DocumentStorageDriver", "XmlTObjDrivers_IntSparseArrayDriver", "XmlTObjDrivers_ModelDriver", "XmlTObjDrivers_ObjectDriver", "XmlTObjDrivers_ReferenceDriver", "XmlTObjDrivers_XYZDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmltobjdrivers"], "children": []}, {"name": "OCC.Core.XmlXCAFDrivers", "type": "module", "classes": ["SwigPyIterator", "XmlXCAFDrivers_DocumentRetrievalDriver", "XmlXCAFDrivers_DocumentStorageDriver", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "ostream", "xmlxcafdrivers"], "children": []}, {"name": "OCC.Core._AIS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._APIHeaderSection", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Adaptor2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Adaptor3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Addons", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AdvApp2Var", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AdvApprox", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppBlend", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppCont", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppDef", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppParCurves", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._AppStdL", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Approx", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ApproxInt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Aspect", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BOPAlgo", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BOPDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BOPTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepAdaptor", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepAlgo", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepAlgoAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepApprox", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepBlend", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepBndLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepBuilderAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepCheck", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepClass", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepClass3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepExtrema", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepFeat", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepFill", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepFilletAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepGProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepIntCurveSurface", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepLProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepMAT2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepMesh", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepMeshData", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepOffset", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepOffsetAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepPrim", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepPrimAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepProj", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepSweep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BRepTopAdaptor", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BSplCLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BSplSLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BVH", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BiTgte", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinLDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMDF", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMDataStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMDataXtd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMDocStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMFunction", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMNaming", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinMXCAFDoc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinObjMgt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinTObjDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BinXCAFDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Bisector", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Blend", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BlendFunc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Bnd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._BndLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._CDF", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._CDM", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._CPnts", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._CSLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ChFi2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ChFi3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ChFiDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ChFiKPart", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Contap", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Convert", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._DE", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._DEBRepCascade", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._DEXCAFCascade", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Draft", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._DsgPrs", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ElCLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ElSLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Expr", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ExprIntrp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Extrema", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._FEmTool", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._FSD", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._FairCurve", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._FilletSurf", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GC", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GCE2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GCPnts", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GccAna", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GccEnt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GccInt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dAdaptor", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dConvert", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dEvaluator", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dGcc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dHatch", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dInt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Geom2dLProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomAbs", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomAdaptor", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomConvert", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomEvaluator", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomFill", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomInt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomLProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomPlate", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomProjLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomToStep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._GeomTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Graphic3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HLRAlgo", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HLRAppli", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HLRBRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HLRTopoBRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Hatch", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HatchGen", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._HeaderSection", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Hermit", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IFSelect", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IGESCAFControl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IGESControl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IGESData", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IGESToBRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IMeshData", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IMeshTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Image", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntAna", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntAna2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntCurve", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntCurveSurface", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntCurvesFace", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntImp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntImpParGen", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntPatch", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntPolyh", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntRes2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntStart", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntSurf", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._IntWalk", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Interface", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._InterfaceGraphic", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Intf", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Intrv", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._LDOM", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._LProp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._LProp3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Law", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._LocOpe", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._LocalAnalysis", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._MAT", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._MAT2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Media", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._MeshDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._MeshVS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Message", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._MoniTool", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._NCollection", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._NLPlate", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._OSD", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._PCDM", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._PLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Plate", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Plugin", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Poly", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Precision", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ProjLib", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Prs3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._PrsDim", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._PrsMgr", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Quantity", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWGltf", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWHeaderSection", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWMesh", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWObj", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWPly", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepAP203", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepAP214", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepAP242", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepBasic", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepDimTol", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepElement", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepFEA", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepGeom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepKinematics", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepRepr", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepShape", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStepVisual", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._RWStl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Resource", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._STEPCAFControl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._STEPConstruct", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._STEPControl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._STEPEdit", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._STEPSelections", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Select3D", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._SelectBasics", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._SelectMgr", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeAlgo", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeAnalysis", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeBuild", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeConstruct", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeCustom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeExtend", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeFix", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeProcess", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeProcessAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._ShapeUpgrade", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Standard", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StdFail", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StdPrs", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StdSelect", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepAP203", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepAP209", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepAP214", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepAP242", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepBasic", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepData", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepDimTol", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepElement", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepFEA", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepGeom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepKinematics", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepRepr", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepShape", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepToGeom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepToTopoDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StepVisual", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._StlAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Storage", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Sweep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TColGeom", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TColGeom2d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TColQuantity", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TColStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TColgp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TCollection", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TDF", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TDataStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TDataXtd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TDocStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TFunction", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TNaming", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TObj", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TPrsStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TShort", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Tesselator", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopAbs", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopBas", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopClass", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopCnx", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopExp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopLoc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopOpeBRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopOpeBRepBuild", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopOpeBRepDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopOpeBRepTool", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopTools", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopTrans", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopoDS", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TopoDSToStep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Transfer", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._TransferBRep", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._UTL", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Units", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._UnitsAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._UnitsMethods", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._V3d", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Visualization", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._Vrml", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._VrmlAPI", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._VrmlConverter", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._VrmlData", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XBRepMesh", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFApp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFDimTolObjects", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFDoc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFNoteObjects", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFPrs", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XCAFView", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XSControl", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlLDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMDF", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMDataStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMDataXtd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMDocStd", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMFunction", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMNaming", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlMXCAFDoc", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlObjMgt", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlTObjDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._XmlXCAFDrivers", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._gce", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._gp", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core._math", "type": "module", "classes": [], "children": []}, {"name": "OCC.Core.gce", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "gce_ErrorType", "gce_MakeCirc", "gce_MakeCirc2d", "gce_MakeCone", "gce_MakeCylinder", "gce_MakeDir", "gce_MakeDir2d", "gce_MakeElips", "gce_MakeElips2d", "gce_MakeHypr", "gce_MakeHypr2d", "gce_MakeLin", "gce_MakeLin2d", "gce_MakeMirror", "gce_MakeMirror2d", "gce_MakeParab", "gce_MakeParab2d", "gce_MakePln", "gce_MakeRotation", "gce_MakeRotation2d", "gce_MakeScale", "gce_MakeScale2d", "gce_MakeTranslation", "gce_MakeTranslation2d", "gce_Root", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.gp", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "gp", "gp_Ax1", "gp_Ax2", "gp_Ax22d", "gp_Ax2d", "gp_Ax3", "gp_Circ", "gp_Circ2d", "gp_Cone", "gp_Cylinder", "gp_Dir", "gp_Dir2d", "gp_Elips", "gp_Elips2d", "gp_EulerSequence", "gp_GTrsf", "gp_GTrsf2d", "gp_Hypr", "gp_Hypr2d", "gp_Lin", "gp_Lin2d", "gp_Mat", "gp_Mat2d", "gp_Parab", "gp_Parab2d", "gp_Pln", "gp_Pnt", "gp_Pnt2d", "gp_Quaternion", "gp_QuaternionNLerp", "gp_QuaternionSLerp", "gp_Sphere", "gp_Torus", "gp_Trsf", "gp_Trsf2d", "gp_TrsfForm", "gp_Vec", "gp_Vec2d", "gp_Vec2f", "gp_Vec3f", "gp_XY", "gp_XYZ", "ios", "ios_base", "iostream", "istream", "ostream"], "children": []}, {"name": "OCC.Core.math", "type": "module", "classes": ["SwigPyIterator", "_SwigNonDynamicMeta", "ios", "ios_base", "iostream", "istream", "math", "math_Array1OfValueAndWeight", "math_BFGS", "math_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "math_BracketMinimum", "math_BracketedRoot", "math_BrentMinimum", "math_BullardGenerator", "math_ComputeGaussPointsAndWeights", "math_ComputeKronrodPointsAndWeights", "math_Crout", "math_DirectPolynomialRoots", "math_DoubleTab", "math_EigenValuesSearcher", "math_FRPR", "math_Function", "math_FunctionAllRoots", "math_FunctionRoot", "math_FunctionRoots", "math_FunctionSample", "math_FunctionSet", "math_FunctionSetRoot", "math_FunctionSetWithDerivatives", "math_FunctionWithDerivative", "math_<PERSON><PERSON>s", "math_GaussLeastSquare", "math_GaussMultipleIntegration", "math_GaussSetIntegration", "math_GaussSingleIntegration", "math_GlobOptMin", "math_Householder", "math_Jacobi", "math_KronrodSingleIntegration", "math_Matrix", "math_MultipleVarFunction", "math_MultipleVarFunctionWithGradient", "math_MultipleVarFunctionWithHessian", "math_NewtonFunctionRoot", "math_NewtonFunctionSetRoot", "math_NewtonMinimum", "math_PSO", "math_PSOParticlesPool", "math_Powell", "math_SVD", "math_Status", "math_TrigonometricEquationFunction", "math_TrigonometricFunctionRoots", "math_Uzawa", "math_ValueAndWeight", "math_Vector", "ostream"], "children": []}]}, {"name": "OCC.Display", "type": "package", "classes": [], "children": [{"name": "OCC.Display.OCCViewer", "type": "module", "classes": ["OffscreenRenderer", "Viewer3d"], "children": []}, {"name": "OCC.Display.SimpleGui", "type": "module", "classes": [], "children": []}, {"name": "OCC.Display.WebGl", "type": "package", "classes": [], "children": [{"name": "OCC.Display.WebGl.flask_server", "type": "error", "error": "No module named 'threejs_renderer'", "classes": [], "children": []}, {"name": "OCC.Display.WebGl.jupyter_renderer", "type": "module", "classes": ["Axes", "BoundingBox", "CustomMaterial", "Grid", "Helpers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NORMAL"], "children": []}, {"name": "OCC.Display.WebGl.simple_server", "type": "module", "classes": [], "children": []}, {"name": "OCC.Display.WebGl.threejs_renderer", "type": "module", "classes": ["HTMLHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "children": []}, {"name": "OCC.Display.WebGl.x3dom_renderer", "type": "module", "classes": ["HTMLBody", "HTMLHeader", "X3DExporter", "X3DomRenderer"], "children": []}]}, {"name": "OCC.Display.backend", "type": "module", "classes": [], "children": []}, {"name": "OCC.Display.qtDisplay", "type": "error", "error": "no backend has been imported yet with ``load_backend``... ", "classes": [], "children": []}, {"name": "OCC.Display.tkDisplay", "type": "module", "classes": ["tkViewer3d"], "children": []}, {"name": "OCC.Display.wxDisplay", "type": "module", "classes": ["wxBase<PERSON>iewer", "wxViewer3d"], "children": []}]}, {"name": "OCC.Extend", "type": "package", "classes": [], "children": [{"name": "OCC.Extend.DataExchange", "type": "module", "classes": [], "children": []}, {"name": "OCC.Extend.LayerManager", "type": "module", "classes": ["Layer"], "children": []}, {"name": "OCC.Extend.ShapeFactory", "type": "module", "classes": [], "children": []}, {"name": "OCC.Extend.TopologyUtils", "type": "module", "classes": ["TopologyExplorer", "WireExplorer"], "children": []}]}, {"name": "OCC.Wrapper", "type": "package", "classes": [], "children": [{"name": "OCC.Wrapper.wrapper_utils", "type": "module", "classes": ["Proxy"], "children": []}]}]}