# 产品需求文档 (PRD): OCCT Python 学习库

**版本**: 1.0

**日期**: 2025-08-17

## 1. 项目概述

### 1.1. 项目目标

本项目旨在为 Open CASCADE Technology (OCCT) 的 Python 封装库 (`pythonocc-core`) 创建一个高质量、系统化、对初学者友好的学习资源库。目标是让开发者能够通过本库，快速掌握OCCT的核心功能，并能上手进行实际的CAD应用开发。

### 1.2. 目标用户

- 希望入门几何建模和CAD开发的Python程序员。
- 有一定CAD基础，希望学习如何在Python环境中使用OCCT的工程师或学生。
- 需要在项目中集成OCCT功能，并寻求代码示例和最佳实践的开发者。

### 1.3. 核心交付物

- **结构化文档**: 一系列Markdown (`.md`) 文件，系统性地介绍OCCT的核心模块。
- **可运行示例**: 与文档一一对应的Python (`.py`) 脚本，演示各模块的核心功能。
- **清晰的项目结构**: 易于导航和扩展的目录结构。

---

## 2. 功能与要求

### 2.1. 目录结构要求

- **分离原则**: 所有文档必须存放在 `OCC/` 目录下，所有示例代码必须存放在 `src/` 目录下。
- **层级镜像**: `OCC/` 和 `src/` 的内部目录结构必须严格按照 `occ_structure.json` 中定义的OCCT包层级进行组织 (例如: `OCC/Core/gp` 对应 `src/Core/gp`)。

### 2.2. 文档要求 (Documentation)

- **模块化**: 每个核心的OCCT模块都应有其独立的介绍文档 (如 `gp.md`)。
- **内容清晰**: 文档需清晰地解释模块的用途、核心概念，并列出最关键的类和函数。
- **语言**: 中文。

### 2.3. 示例代码要求 (Example Code)

- **功能对应**: 每个示例文件都必须清晰、直接地演示其对应文档中介绍的功能。
- **代码质量**: 代码必须整洁、易读，并包含充分的中文注释以解释关键步骤。
- **可运行性**: 所有示例代码必须经过验证，确保在指定的项目环境中可以无错误地运行。

### 2.4. 项目管理要求

- **依赖管理**: 项目的所有Python依赖必须在 `requirements.txt` 文件中明确列出。
- **版本控制**: 项目需包含一个标准的 `.gitignore` 文件，以排除不必要的文件。
- **开发计划**: 项目需包含一个 `plan.md` 文件，用于定义和追踪学习/开发路线图。
- **阶段示例**: 每个学习阶段都必须提供一个存放在 `examples/` 目录下的汇总示例脚本，用于整合和展示该阶段学习的全部核心技术。

---

## 3. 开发计划与学习路线

项目将严格遵循以下从基础到高级的阶段化路线图，逐一完成模块的文档和代码创建。

### 第一阶段：核心概念入门

- **目标**: 掌握OCCT进行基础建模和可视化的完整工作流程。
- **状态**: <span style="color:green">**已完成**</span>
- **模块**: `gp`, `TopoDS`, `BRepPrimAPI`, `BRepAlgoAPI`, `AIS` & `V3d`
- **汇总示例**: `examples/phase_1_core_concepts.py`

### 第二阶段：进阶边界表示建模

- **目标**: 学习使用更灵活的工具来构建复杂的、非标准的几何形状。
- **状态**: <span style="color:orange">**待办**</span>
- **模块**: `BRepBuilderAPI`, `BRepFilletAPI`, `BRepOffsetAPI`, `BRepTools`
- **汇总示例**: `examples/phase_2_advanced_modeling.py` (待创建)

### 第三阶段：数据交换与网格化

- **目标**: 学习OCCT模型与标准CAD文件格式的交互，并生成网格数据。
- **状态**: <span style="color:orange">**待办**</span>
- **模块**: `STEPControl`, `IGESControl`, `BRepMesh`, `StlAPI`
- **汇总示例**: `examples/phase_3_interoperability.py` (待创建)

### 第四阶段：分析与查询

- **目标**: 学习如何从几何模型中提取有用的信息和数据。
- **状态**: <span style="color:orange">**待办**</span>
- **模块**: `BRepGProp`, `BRepExtrema`, `BRepCheck`
- **汇总示例**: `examples/phase_4_analysis.py` (待创建)

## 4. 技术规格

- **编程语言**: Python 3.x
- **核心库**: `pythonocc-core==7.9.0`
- **运行环境**: Conda 虚拟环境，环境名 `occ`。
- **结构参考**: `occ_structure.json` 文件。

---

## 5. 后续任务与未来展望 (V2.0+)

在完成V1.0的基础学习路径后，可规划以下任务以扩展项目：

- **丰富模块覆盖**: 继续按照 `occ_structure.json`，为更多重要模块（如 `BRepBuilderAPI`, `BRepOffsetAPI`, `BRepMesh`）创建文档和示例。
- **建立测试框架**: 引入 `pytest` 等测试框架，为 `src/` 目录下的所有示例编写自动化测试用例，确保代码库的长期稳定性和正确性。
- **增加综合项目**: 创建更复杂的综合性示例（如齿轮、支架等），演示如何组合运用多个模块来解决实际问题。
- **完善主页**: 编写项目根目录下的 `README.md`，作为整个学习库的入口和导航。
