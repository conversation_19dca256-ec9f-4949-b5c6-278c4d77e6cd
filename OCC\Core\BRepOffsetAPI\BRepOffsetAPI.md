# `BRepOffsetAPI` (Boundary Representation Offset API)

`BRepOffsetAPI` 提供了一系列算法，用于从一个已有的形状（或其一部分）创建出偏移后的新形状。这在需要创建具有均匀壁厚的物体（如管材、容器）或为模具设计添加拔模斜度时非常有用。

这个API的核心思想是“偏移”——将一个形状的表面向内或向外移动指定的距离。

## 核心用法

`BRepOffsetAPI` 的使用模式与其他 `BRep...API` 类似，但其构造函数通常需要更多的参数来精确控制偏移的行为。

以最常用的“抽壳”为例，其使用流程如下：

1.  **准备基础形状**: 首先，你需要一个闭合的实体（`TopoDS_Solid`）作为抽壳的基础。

2.  **选择要移除的面 (可选)**: 确定在抽壳后，哪些面需要被移除以形成开口。你可以选择一个、多个或不选择任何面。如果不选择任何面，你将得到一个完全封闭的中空物体。

3.  **实例化 `MakeThickSolid`**: `mk_thick = BRepOffsetAPI_MakeThickSolid(my_solid, faces_to_remove, offset, tolerance)`
    *   `my_solid`: 你的基础实体。
    *   `faces_to_remove`: 一个包含 `TopoDS_Face` 的列表。可以是一个空列表。
    *   `offset`: 偏移距离，即壁厚。**一个负值通常表示向内偏移（即向实体内部“增厚”形成空腔）**。
    *   `tolerance`: 计算容差。

4.  **执行构建**: 调用 `.Build()` 方法。

5.  **获取结果**: 调用 `.Shape()` 方法获取最终的中空形状。

## 主要类

*   **`BRepOffsetAPI_MakeThickSolid`**: 这是实现“抽壳”功能的核心类。它通过向内或向外偏移实体的所有面，然后移除指定的面来创建一个有壁厚的实体。

*   **`BRepOffsetAPI_MakeOffsetShape`**: 一个更通用的偏移工具，可以对任何类型的形状（点、线、面、壳、实体）进行偏移，生成一个平行的形状。

*   **`BRepOffsetAPI_MakeDraft`**: 用于为形状的侧面添加拔模角度，这在铸造和注塑模具设计中是必不可少的特征。

## 注意事项

- **偏移方向**: `offset` 值的正负号决定了偏移方向。对于一个闭合实体，负值通常意味着向体积内部偏移，从而“挖”出空腔。
- **几何限制**: 与圆角一样，偏移距离也受到几何形状的限制。如果偏移距离过大，导致不同表面的偏移结果发生自交或冲突，构建就可能会失败。

在接下来的示例中，我们将创建一个长方体，并使用 `MakeThickSolid` 将其变成一个开口的、有壁厚的盒子。
