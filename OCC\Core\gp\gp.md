# `gp` (Geometric Primitives)

`gp` 是 Open CASCADE Technology (OCCT) 的核心基础模块之一，提供了所有几何计算所需的基本构建块。可以将其理解为2D和3D几何空间的数学基础库。

几乎所有更高级的操作，无论是创建形状、进行变换还是执行分析，都依赖于 `gp` 包中定义的类。

## 核心概念

`gp` 包主要围绕以下几个核心概念：

1.  **点 (Points)**: 定义在2D或3D空间中的一个位置。
2.  **向量 (Vectors)**: 表示方向和大小，用于平移、计算方向等。
3.  **坐标系 (Coordinate Systems)**: 定义空间中的位置和方向，作为几何对象的参考。
4.  **几何变换 (Geometric Transformations)**: 包括平移、旋转、缩放、镜像等操作。这些变换可以应用于几何对象上。

## 主要类

以下是 `gp` 包中最常用的一些类：

*   `gp_Pnt`: 表示一个3D笛卡尔坐标点 (e.g., `gp_Pnt(X, Y, Z)`).
*   `gp_Pnt2d`: 表示一个2D笛卡尔坐标点 (e.g., `gp_Pnt2d(X, Y)`).
*   `gp_Vec`: 表示一个3D向量。
*   `gp_Vec2d`: 表示一个2D向量。
*   `gp_Dir`: 表示一个3D方向（单位向量）。
*   `gp_Dir2d`: 表示一个2D方向。
*   `gp_Ax1`, `gp_Ax2`, `gp_Ax3`: 分别表示轴、2D坐标系和3D坐标系。
*   `gp_Trsf`: 表示一个3D仿射变换（可以包含平移、旋转、缩放的组合）。
*   `gp_GTrsf`: 表示一个更通用的3D变换（可以包含非线性变换）。

掌握 `gp` 包是学习OCCT的第一步，也是最重要的一步。在接下来的示例代码中，我们将演示如何创建和使用这些基础几何图元。
